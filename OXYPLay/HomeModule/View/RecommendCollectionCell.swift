protocol RecommendCollectionCellDelegate: AnyObject {
    /// 点赞按钮点击事件
    /// - Parameter cell: 点击事件发生的单元格
    func recommendCollectionCellDidTapLike(_ model: RecommendModel)
}
class RecommendCollectionCell: UICollectionViewCell {
    // MARK: - UI组件懒加载
    /// 代理
    weak var delegate: RecommendCollectionCellDelegate?

    private var model: RecommendModel?
     lazy var mainImageView: UIImageView = {
        let iv = UIImageView()
         iv.contentMode = .scaleAspectFill
        iv.backgroundColor = .secondarySystemBackground
        iv.layer.cornerRadius = 8
        iv.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        iv.masksToBounds = true
        return iv
    }()
     lazy var typeButton: UIButton = {
        let iv = UIButton()
        iv.backgroundColor = UIColor(hexString: "000000",transparency: 0.32)
        iv.layer.cornerRadius = 8
        iv.setTitleColor(.white, for: .normal)
        iv.titleLabel?.font = .systemFont(ofSize: 11, weight: .medium)
        return iv
    }()
     lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = color_3D3E40
        label.numberOfLines = 2
        return label
    }()
     lazy var priceLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textColor = UIColor(hexString: "FF0000")
        return label
    }()
     lazy var stack: UIStackView = {
        let label = UIStackView()
        label.spacing = 8
        label.axis = .vertical
        return label
    }()
     lazy var avatarImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.backgroundColor = .random
        iv.layer.cornerRadius = 9
        return iv
    }()

     lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = color_3D3E40
        return label
    }()
    private lazy var likeButton = BaseButton().then {
        $0.setImage(UIImage(named: "home_like_select")?.resize(to: CGSize(width: 12, height: 11)), for: .selected)
        $0.setImage(UIImage(named: "home_like")?.resize(to: CGSize(width: 12, height: 11)), for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        $0.setTitleColor(color_2B2C2F72, for: .normal)
        $0.imagePosition = .left
        $0.spacing = 4
        $0.addTarget(self, action: #selector(likeButtonDidClick), for: .touchUpInside)
    }

    @objc func likeButtonDidClick(){
        guard let model = model else { return}
        self.delegate?.recommendCollectionCellDidTapLike(model)
    }

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - UI配置

     func setupUI() {
        contentView.layer.cornerRadius = 12
        contentView.layer.masksToBounds = true
        contentView.backgroundColor = .white
         contentView.addSubviews([mainImageView, stack, avatarImageView, usernameLabel, likeButton] )
        stack.addArrangedSubview(titleLabel)
        stack.addArrangedSubview(priceLabel)
        mainImageView.addSubview(typeButton)
    }

    // MARK: - 自动布局

     func setupConstraints() {
        mainImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(0)
            make.height.equalTo(mainImageView.snp.width).dividedBy(1.0)
        }

        stack.snp.makeConstraints { make in
            make.top.equalTo(mainImageView.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(12)
        }

        avatarImageView.snp.makeConstraints { make in
            make.top.equalTo(stack.snp.bottom).offset(8)
            make.left.equalToSuperview().inset(12)
            make.width.height.equalTo(18)
        }

        usernameLabel.snp.makeConstraints { make in
            make.centerY.equalTo(avatarImageView)
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.right.lessThanOrEqualTo(likeButton.snp.left).offset(-8)
        }

         likeButton.snp.makeConstraints { make in
            make.centerY.equalTo(avatarImageView)
            make.right.equalToSuperview().inset(12)
        }
        
        typeButton.snp.makeConstraints { make in
            make.top.left.equalTo(8)
            make.height.equalTo(20)
            make.width.equalTo(typeButton.intrinsicContentSize.width + 10)
        }
    }
    
    
    // 配置RecommendModel数据
    func configure(with model: RecommendModel) {
        self.model = model
        mainImageView.setImage(url: model.img)
        priceLabel.text = model.price.formattedPrice
        priceLabel.isHidden = model.price.isEmpty
        titleLabel.text = model.title
        usernameLabel.text = model.user?.nickname
        likeButton.setTitle("\(model.like_count)", for: .normal)
        likeButton.isSelected = model.is_liked
        typeButton.setTitle(model.recommendType.displayName, for: .normal)
        avatarImageView.setImage(url: model.user?.avatar)
        mainImageView.snp.remakeConstraints { make in
            make.top.left.right.equalToSuperview().inset(0)
            make.height.equalTo(mainImageView.snp.width).dividedBy(model.imageSize)
        }
        typeButton.snp.updateConstraints { make in
            make.width.equalTo(typeButton.intrinsicContentSize.width + 10)
        }
    }
    
    // MARK: - 静态高度计算方法
    
    /// 静态方法计算单元格高度
    /// - Parameters:
    ///   - model: 推荐模型
    ///   - width: 单元格宽度
    /// - Returns: 计算后的高度
    static   func calculateHeight(for model: RecommendModel, width: CGFloat) -> CGFloat {
        // 图片高度比例 (可以根据实际情况调整)
        let imageHeight = width / model.imageSize
        var bottomHeigt = 80.0
        if model.price.count > 0{
            bottomHeigt = bottomHeigt + 20
        }
        if !isLabelWrappingRequired(text: model.title, font:UIFont.systemFont(ofSize: 14, weight: .regular) , labelWidth: (kScreenWidth - 24 - 9.5)/2){
            bottomHeigt =  bottomHeigt - 18
        }
        let totalHeight = imageHeight + bottomHeigt
        
        return ceil(totalHeight)
    }
    static func isLabelWrappingRequired(text: String,
                                 font: UIFont,
                                 labelWidth: CGFloat) -> Bool {
        // 1. 计算单行文本所需宽度
        let attributes: [NSAttributedString.Key: Any] = [.font: font]
        let singleLineWidth = (text as NSString).size(withAttributes: attributes).width
        
        // 2. 考虑布局边距（系统自动添加约4pt内边距）
        let systemPadding: CGFloat = 4.0
        let effectiveLabelWidth = labelWidth - systemPadding
        
        // 3. 判断是否需要换行
        return singleLineWidth > effectiveLabelWidth
    }
}

