//
//  DetailCommentTitleTableCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/2.
//

import UIKit
import SnapKit
import Then
protocol DetailCommentTitleTableCellDelegate: AnyObject {
    func moreCommentButtonClick()
}

class DetailCommentTitleTableCell: UITableViewCell {
    weak var delegate: DetailCommentTitleTableCellDelegate?

    // MARK: - UI组件
    
    private lazy var containerView = UIView()
    
    private lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = color_3D3E40
        $0.text = "评论"
    }
    
    private lazy var countLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = color_3D3E40
    }
    lazy var moreButton = BaseButton().then {
        $0.setTitle("查看全部", for: .normal)
        $0.setTitleColor(color_999999, for: .normal)
        $0.imagePosition = .right
        $0.spacing = 4
        $0.setImage(UIImage(named: "home_product_detail_right_arrow"), for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12)
    }
    // 添加底部分割线
    let separatorLine = UIView()
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left:12, bottom: 0, right: 12))
        }
        
        containerView.addSubview(titleLabel)
        
        containerView.addSubview(countLabel)
        containerView.addSubview(moreButton)

        separatorLine.backgroundColor = UIColor(hexString: "2B2C2F",transparency: 0.08)
        contentView.addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(1)
            make.top.equalTo(0)
        }
        countLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel.snp.right).offset(6)
            make.centerY.equalTo(titleLabel)
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(separatorLine.snp.bottom).offset(16)
            make.left.equalToSuperview()
            make.bottom.equalTo(-16)
        }
        moreButton.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalTo(0)
        }
        moreButton.addTarget(self, action: #selector(moreButtonTapped), for: .touchUpInside)

        
    }
    
    // MARK: - 配置方法
    
    func configure(with commentCount: Int) {
        countLabel.text = "\(commentCount)"
    }
    
    // MARK: - 事件处理
    
    @objc private func moreButtonTapped() {
        // 处理查看全部按钮点击事件
        self.delegate?.moreCommentButtonClick()
    }
    
} 
