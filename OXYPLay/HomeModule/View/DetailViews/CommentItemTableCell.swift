//
//  CommentItemTableCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/2.
//

import UIKit


class CommentItemTableCell: UITableViewCell {
    weak var delegate: DetailCellDelegate?
    
    // MARK: - UI组件
    private lazy var model = CommentModel()

    private lazy var containerView = UIView()
    
    private lazy var avatarImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
        $0.layer.cornerRadius = 16
    }
    
    private lazy var nameLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = UIColor(hexString:"3D3E40", transparency: 0.72)
    }
    
    private lazy var timeLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = UIColor(hexString:"3D3E40", transparency: 0.4)
    }
    
    private lazy var contentLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.textColor = UIColor(hexString:"3D3E40", transparency: 1)
        $0.numberOfLines = 0
    }
    
    private lazy var likeButton = BaseButton().then {
        $0.setImage(UIImage(named: "home_like_select")?.resize(to: CGSize(width: 17, height: 15)), for: .selected)
        $0.setImage(UIImage(named: "home_like")?.resize(to: CGSize(width: 17, height: 15)), for: .normal)
        $0.imagePosition = .top
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12,weight: .regular)
        $0.setTitleColor(UIColor(hexString: "2B2C2F",transparency: 0.64), for: .normal)
    }
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left:12, bottom: 21, right: 12))
        }
        
        containerView.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        // 先添加likeButton到视图层次结构中
        containerView.addSubview(likeButton)
        likeButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.right.equalToSuperview()
        }
        
        // 然后再添加nameLabel并设置与likeButton的约束
        containerView.addSubview(nameLabel)
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView)
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.right.lessThanOrEqualTo(likeButton.snp.left).offset(-10)
        }
        
        containerView.addSubview(timeLabel)
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(7)
            make.left.equalTo(nameLabel)
        }
        
        containerView.addSubview(contentLabel)
        contentLabel.snp.makeConstraints { make in
            make.top.equalTo(timeLabel.snp.bottom).offset(7)
            make.left.equalTo(nameLabel)
            make.right.equalToSuperview().offset(-15)
            make.bottom.equalToSuperview()
        }
                
        likeButton.addTarget(self, action: #selector(likeButtonTapped), for: .touchUpInside)
    }
    
    // MARK: - 配置方法
    
    func configure(with comment: CommentModel) {
        model = comment
        nameLabel.text = comment.user.nickname
        timeLabel.text = comment.created_at.formatTimeAgo
        contentLabel.text = comment.content
        
        // 设置点赞状态和数量
        likeButton.isSelected = comment.is_liked
        likeButton.setTitle(" \(comment.like_count)", for: .normal)
        
        // 设置头像
        avatarImageView.setImage(url:comment.user.avatar)
    }

    // MARK: - 事件处理
    
    @objc private func likeButtonTapped() {
        likeButton.isSelected = !likeButton.isSelected
        
        // 更新点赞数
        if let currentTitle = likeButton.title(for: .normal),
           let count = Int(currentTitle.trimmingCharacters(in: .whitespaces)) {
            let newCount = likeButton.isSelected ? count + 1 : max(0, count - 1)
            likeButton.setTitle(" \(newCount)", for: .normal)
        }
        
        self.delegate?.likeButtonDidClick(model: model)
    }
    
} 
