//
//  DetailNavBarView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/3.
//

import UIKit
import CombineCocoa
import Combine

protocol DetailNavBarViewDelegate: AnyObject {
    func navBarViewDidTapBack(_ navBarView: DetailNavBarView)
    func navBarViewDidTapUser(_ navBarView: DetailNavBarView)
    func navBarViewDidTapChat(_ navBarView: DetailNavBarView)
    func navBarViewDidTapFollow(_ navBarView: DetailNavBarView)
    func navBarViewDidTapMore(_ navBarView: DetailNavBarView)
}

class DetailNavBarView: BaseView {
    
    // MARK: - 属性
    
    weak var delegate: DetailNavBarViewDelegate?
    
    // MARK: - UI组件
    
    lazy var backItem = BaseButton().then{
        $0.setImage(UIImage(systemName: "chevron.left")?.withAlwaysOriginalTintColor(.black), for: .normal)
    }
    
    lazy var userItem =  BaseButton().then{
        $0.spacing = 12
        $0.titleLabel?.font = .systemFont(ofSize: 15, weight: .medium)
        $0.setTitleColor(color_2B2C2F, for: .normal)
    }
    lazy var chatItem =  BaseButton().then{
        $0.setTitle("聊一聊", for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 12, weight: .regular)
        $0.setTitleColor(color_blue, for: .normal)
        $0.borderColor = color_blue
        $0.borderWidth = 1.0
        $0.isHidden = true
        $0.isRounded = true
    }
    
    // 关注按钮
    private lazy var followItem: BaseButton = {
        let button = BaseButton()
        button.setTitleColor(.white, for: .normal)
        button.setTitleColor(color_2B2C2F64, for: .selected)
        button.setBackgroundColor(color_blue, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        button.setBackgroundColor(UIColor(hexString: "788092", transparency: 0.08)!, for: .selected)
        button.isRounded = true
        button.isHidden = true
        return button
    }()
    lazy var moreItem = BaseButton().then{
        $0.setImage(UIImage(named: "home_detail_nav_more"), for: .normal)
    }
    
    // MARK: - 初始化方法
    func configProduct(){
        [userItem, chatItem, followItem].forEach { button in
            button.isHidden = true
        }
        moreItem.setImage(UIImage(named: "home_product_detail_more"), for: .normal)
        self.backgroundColor = .clear
    }
    override func configUI() {
        addSubviews([backItem, userItem, chatItem, followItem, moreItem])
        setupBindings()
    }
    
    override func configLayout() {
        backItem.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.centerY.equalTo(userItem)
        }
        userItem.snp.makeConstraints { make in
            make.left.equalTo(backItem.snp.right).offset(8)
            make.height.equalTo(32)
            make.bottom.equalTo(-10)
        }
        moreItem.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalTo(userItem)
            make.width.height.equalTo(28)
        }
        followItem.snp.makeConstraints { make in
            make.right.equalTo(moreItem.snp.left).offset(-12)
            make.centerY.equalTo(userItem)
            make.height.equalTo(28)
            make.width.equalTo(60)
        }
        chatItem.snp.makeConstraints { make in
            make.right.equalTo(followItem.snp.left).offset(-12)
            make.centerY.equalTo(userItem)
            make.height.equalTo(28)
            make.width.equalTo(60)
        }
    }
    
    // MARK: - 绑定事件
    
   override func setupBindings() {
        backItem.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.navBarViewDidTapBack(self)
            }
            .store(in: &cancellables)
        
        userItem.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.navBarViewDidTapUser(self)
            }
            .store(in: &cancellables)
        
        chatItem.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.navBarViewDidTapChat(self)
            }
            .store(in: &cancellables)
        
        followItem.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.navBarViewDidTapFollow(self)
            }
            .store(in: &cancellables)
        
        moreItem.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.navBarViewDidTapMore(self)
            }
            .store(in: &cancellables)
    }
    func configUser(userName:String,userAvatar:String,is_following:Bool){
        userItem.setTitle(userName, for: .normal)
        followItem.isSelected = is_following
        followItem.setImage(is_following ? nil : UIImage(named: "addfriend_add"), for: .normal)
        followItem.setTitle(is_following ? "已关注" : "关注", for: .normal)
        if let imageUrl = URL(string: userAvatar) {
            userItem.kf.setImage(with: imageUrl, for: .normal,placeholder: UIImage(named: "有话说"))
        }

    }
    func isSelfPush(userId:String){
        if let umodel = UserManager.shared.getCurrentUser(){
            followItem.isHidden = umodel.id == userId
            chatItem.isHidden = umodel.id == userId
        }
    }
}
