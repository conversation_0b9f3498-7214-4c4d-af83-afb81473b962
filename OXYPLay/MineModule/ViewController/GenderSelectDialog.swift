//
//  GenderSelectDialog.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit
import SnapKit
import Then
import Combine
import CombineCocoa

/// 性别选择弹窗
class GenderSelectDialog: BasePresentController {
    
    // MARK: - Properties
    
    /// 选择完成回调
    let selectionCompletedPublisher = PassthroughSubject<(Int, Bool), Never>()
    
    /// 当前选中的性别
    private var currentGender: Int = 1
    
    /// 是否公开显示
    private var isPublicDisplay: Bool = true
    
    // MARK: - UI Components
    /// 男性选项
    private lazy var OptionView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 13
    }
    /// 男性选项
    private lazy var maleOptionView = UIView()
    
    /// 男性单选按钮
    private lazy var maleRadioButton = UIButton().then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        $0.isSelected = true
    }
    
    /// 男性标签
    private lazy var maleLabel = UILabel().then {
        $0.text = "男"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 13, weight: .regular)
    }
    
    /// 女性选项
    private lazy var femaleOptionView = UIView()
    
    /// 女性单选按钮
    private lazy var femaleRadioButton = UIButton().then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        $0.isSelected = false
    }
    
    /// 女性标签
    private lazy var femaleLabel = UILabel().then {
        $0.text = "女"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 13, weight: .regular)
    }
    
    /// 公开显示容器
    private lazy var publicDisplayContainer = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
    }
    
    /// 公开显示标签
    private lazy var publicDisplayLabel = UILabel().then {
        $0.text = "是否公开展示"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = color_2B2C2F80
    }
    
    /// 显示性别标签标签
    private lazy var showGenderLabelLabel = UILabel().then {
        $0.text = "展示性别标签"
        $0.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        $0.textColor = color_2B2C2F
    }
    
    /// 显示性别标签开关
    private lazy var showGenderLabelSwitch = UISwitch().then {
        $0.onTintColor = color_blue
        $0.isOn = true
    }
    
    // MARK: - 初始化
    
    init(currentGender: Int = 1, isPublicDisplay: Bool = true) {
        self.currentGender = currentGender
        self.isPublicDisplay = isPublicDisplay
        super.init()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override var presentationHeight: CGFloat? {
        return nil
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
    }
    
    // MARK: - UI Setup
    
    override func configUI() {
        configView(title: "编辑性别", bottomTitle: "确认")
        
        // 添加男性选项
        contentView.addSubview(OptionView)
        OptionView.addSubview(maleOptionView)
        maleOptionView.addSubview(maleLabel)
        maleOptionView.addSubview(maleRadioButton)
        
        // 添加女性选项
        OptionView.addSubview(femaleOptionView)
        femaleOptionView.addSubview(femaleLabel)
        femaleOptionView.addSubview(femaleRadioButton)
        
        // 添加公开显示容器
        contentView.addSubview(publicDisplayContainer)
        contentView.addSubview(publicDisplayLabel)
        publicDisplayContainer.addSubview(showGenderLabelLabel)
        publicDisplayContainer.addSubview(showGenderLabelSwitch)
        
        setupConstraints()
        updateGenderSelection()
    }
    
    private func setupConstraints() {
        OptionView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.top.equalToSuperview()
        }
        // 男性选项约束
        maleOptionView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(37)
        }
        
        maleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        maleRadioButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.size.equalTo(24)
        }
        
        // 女性选项约束
        femaleOptionView.snp.makeConstraints { make in
            make.top.equalTo(maleOptionView.snp.bottom).offset(1)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(37)
            make.bottom.equalToSuperview()
        }
        
        femaleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        femaleRadioButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.size.equalTo(24)
        }
        publicDisplayLabel.snp.makeConstraints { make in
            make.top.equalTo(OptionView.snp.bottom).offset(12)
            make.left.equalTo(12)
        }
        // 公开显示容器约束
        publicDisplayContainer.snp.makeConstraints { make in
            make.top.equalTo(publicDisplayLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(37)
            make.bottom.equalToSuperview()
        }
        
        // 公开显示标签约束
        showGenderLabelLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
        }
        
        // 显示性别标签开关约束
        showGenderLabelSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
        }
    }
    
    // MARK: - Bindings
    
    override func setupBindings() {
        super.setupBindings()
        // 添加点击事件
        let tapGesture1 = UITapGestureRecognizer()
        tapGesture1.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.selectGender(1)
            }
            .store(in: &cancellables)
        
        maleOptionView.addGestureRecognizer(tapGesture1)
        
        let tapGesture2 = UITapGestureRecognizer()
        tapGesture2.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.selectGender(2)
            }
            .store(in: &cancellables)
        
        femaleOptionView.addGestureRecognizer(tapGesture2)
        
        // 确认按钮
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.selectionCompletedPublisher.send((self.currentGender, self.showGenderLabelSwitch.isOn))
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods
    
    /// 选择性别
    private func selectGender(_ gender: Int) {
        currentGender = gender
        updateGenderSelection()
    }
    
    /// 更新性别选择状态
    private func updateGenderSelection() {
        maleRadioButton.isSelected = (currentGender == 1)
        femaleRadioButton.isSelected = (currentGender == 2)
    }
    
    // MARK: - Public Methods
    
    /// 配置性别选择弹窗
    /// - Parameters:
    ///   - currentGender: 当前性别 1-男 2-女
    ///   - isPublicDisplay: 是否公开显示
    func configure(currentGender: Int, isPublicDisplay: Bool) {
        self.currentGender = currentGender
        self.isPublicDisplay = isPublicDisplay
        showGenderLabelSwitch.isOn = isPublicDisplay
        updateGenderSelection()
    }
}
