//
//  LocationSelectViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import Foundation
import Combine

class LocationSelectViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 地区列表数据
    @Published var regionList: [RegionListItemModel] = []
    
    /// 当前选中的省份
    @Published var selectedProvince: RegionListItemModel?
    
    /// 当前选中的城市
    @Published var selectedCity: RegionListItemModel?
    
    // MARK: - Initialization
    
    override init() {
        super.init()
    }
    
    // MARK: - Public Methods
    
    /// 获取地区列表
    /// - Parameter parent_code: 父级地区代码，nil表示获取省份列表
    func fetchRegionList(parent_code: String?) {
        requestState = .loading
        
        var params = RequestParameters(["": ""])
        if let parent_code = parent_code {
            params = RequestParameters([
                "parent_code": parent_code,
            ])
        }
        
        requestModel(CommentService.regionList(params: params), type: [RegionListItemModel].self)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                guard let self = self else { return }
                
                switch completion {
                case .finished:
                    self.requestState = .success
                case .failure(let error):
                    self.requestState = .failure(self.handleError(error))
                    self.regionList = []
                }
            } receiveValue: { [weak self] models in
                guard let self = self else { return }
                self.regionList = models
            }
            .store(in: &cancellables)
    }
    
    /// 设置选中的省份
    /// - Parameter province: 省份模型
    func setSelectedProvince(_ province: RegionListItemModel) {
        selectedProvince = province
        selectedCity = nil
    }
    
    /// 设置选中的城市
    /// - Parameter city: 城市模型
    func setSelectedCity(_ city: RegionListItemModel) {
        selectedCity = city
    }
    
    /// 获取完整的地址字符串
    /// - Returns: 完整地址
    func getFullAddress() -> String {
        var address = ""
        
        if let province = selectedProvince {
            address += province.name
        }
        
        if let city = selectedCity, city.name != selectedProvince?.name {
            address += city.name
        }
        
        return address
    }
    
    /// 重置选择状态
    func resetSelection() {
        selectedProvince = nil
        selectedCity = nil
        regionList = []
    }
    
    // MARK: - Private Methods
    
    /// 处理网络错误
    /// - Parameter error: 网络错误
    /// - Returns: 错误描述
    private func handleError(_ error: NetworkError) -> String {
        switch error {
        case .networkError(let response):
            return response.message
        case .decodingError:
            return "数据解析错误"
        case .noConnection:
            return "网络连接失败"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .tokenError:
            return "登录状态异常"
        }
    }
}
