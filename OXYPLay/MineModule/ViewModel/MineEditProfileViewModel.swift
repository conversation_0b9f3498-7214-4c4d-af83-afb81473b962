//
//  MineEditProfileViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import Foundation
import Combine

class MineEditProfileViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 用户信息
    @Published var userInfo: UserModel?
    
    /// 保存结果发布者
    let saveResultPublisher = PassthroughSubject<Bool, Never>()
    
    // MARK: - Initialization
    
    override init() {
        super.init()
    }
    
    // MARK: - Public Methods
    
    /// 加载用户信息
    func loadUserInfo() {
        userInfo = UserManager.shared.getCurrentUser()
    }
    
    /// 保存用户信息
    /// - Parameter formData: 表单数据
    func saveUserInfo(_ formData: [String: Any]) {
        guard let currentUser = userInfo else {
            saveResultPublisher.send(false)
            return
        }
        
        // 构建更新参数
        var params: [String: Any] = [:]
        
        // 昵称
        if let nickname = formData["nickname"] as? String, !nickname.isEmpty {
            params["nickname"] = nickname
        }
        
        // 性别
        if let gender = formData["gender"] as? Int {
            params["gender"] = gender
        }
        
        // 雪龄
        if let skiAge = formData["ski_age"] as? String, !skiAge.isEmpty {
            params["ski_age"] = skiAge
        }
        
        // 地区
        if let location = formData["location"] as? String, !location.isEmpty {
            params["location"] = location
        }
        
        // 个人简介
        if let description = formData["description"] as? String {
            params["description"] = description
        }
        
        // 发起网络请求
        updateUserProfile(params: params)
    }
    
    // MARK: - Private Methods
    
    /// 更新用户资料
    /// - Parameter params: 更新参数
    private func updateUserProfile(params: [String: Any]) {
        requestState = .loading
        
        requestModel(MineService.updateProfile(params: params), type: UserModel.self)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                guard let self = self else { return }
                
                switch completion {
                case .finished:
                    self.requestState = .success
                case .failure(let error):
                    self.requestState = .failure(self.handleError(error))
                    self.saveResultPublisher.send(false)
                }
            } receiveValue: { [weak self] updatedUser in
                guard let self = self else { return }
                
                // 更新本地用户信息
                UserManager.shared.saveUser(updatedUser)
                self.userInfo = updatedUser
                
                // 发送保存成功通知
                self.saveResultPublisher.send(true)
                
                // 发送用户信息更新通知
                NotificationCenter.default.post(name: AppNotifications.userFollowCountUpdated, object: nil)
            }
            .store(in: &cancellables)
    }
    
    /// 处理网络错误
    /// - Parameter error: 网络错误
    /// - Returns: 错误描述
    private func handleError(_ error: NetworkError) -> String {
        switch error {
        case .networkError(let response):
            return response.message
        case .decodingError:
            return "数据解析错误"
        case .noConnection:
            return "网络连接失败"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .tokenError:
            return "登录状态异常"
        }
    }
}
