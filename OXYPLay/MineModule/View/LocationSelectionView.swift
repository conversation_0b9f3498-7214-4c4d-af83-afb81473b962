//
//  LocationSelectionView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/1.
//

import UIKit
import SnapKit
import Then
import Combine
import CombineCocoa

/// 位置选择视图代理协议
protocol LocationSelectionViewDelegate: AnyObject {
    /// 选择地区事件
    func locationSelectionView(_ view: LocationSelectionView, didSelectRegion region: RegionListItemModel)
    /// 省份按钮点击事件
    func locationSelectionViewDidTapProvinceButton(_ view: LocationSelectionView)
    /// 城市按钮点击事件
    func locationSelectionViewDidTapCityButton(_ view: LocationSelectionView)
    /// 判断地区是否被选中
    func locationSelectionView(_ view: LocationSelectionView, isRegionSelected region: RegionListItemModel) -> Bool
}

/// 自定义位置选择视图
class LocationSelectionView: BaseView {

    // MARK: - Properties

    /// 代理对象
    weak var delegate: LocationSelectionViewDelegate?
    
    /// 分组后的地区数据
    private var groupedRegions: [String: [RegionListItemModel]] = [:]
    
    /// 排序后的分组键
    private var sortedSectionKeys: [String] = []
    
    /// 当前选择状态（true: 省份选择, false: 城市选择）
    private var isProvinceSelection: Bool = true

    // MARK: - UI Components

    /// 头部视图
    private lazy var headerView = UIView().then {
        $0.backgroundColor = .white
    }
    
    /// 标题标签
    private lazy var titleLabel = UILabel().then {
        $0.text = "选择省份/地区"
        $0.textColor = color_2B2C2F80
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
    }
    
    /// 省份按钮
    lazy var provinceButton = BaseButton().then {
        $0.setTitle("请选择省份", for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setTitleColor(color_blue, for: .selected)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.isSelected = false
    }

    /// 城市按钮
    lazy var cityButton = BaseButton().then {
        $0.setTitle("请选择城市", for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setTitleColor(color_blue, for: .selected)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.isSelected = false
    }
    
    /// 列表视图
    private lazy var tableView = UITableView(frame: .zero, style: .grouped).then {
        $0.backgroundColor = .white
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self
        $0.register(LocationTableViewCell.self, forCellReuseIdentifier: "LocationTableViewCell")
    }

    // MARK: - UI Configuration

    /// UI配置
    override func configUI() {
        backgroundColor = .white
        layer.cornerRadius = 16
        masksToBounds = true

        // 添加子视图
        addSubview(headerView)
        headerView.addSubview(titleLabel)
        headerView.addSubview(provinceButton)
        headerView.addSubview(cityButton)
        addSubview(tableView)
    }

    /// UI布局
    override func configLayout() {
        // 头部视图约束
        headerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(50)
        }
        
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
        }
        
        // 城市按钮约束
        cityButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
        }
        
        // 省份按钮约束
        provinceButton.snp.makeConstraints { make in
            make.right.equalTo(cityButton.snp.left).offset(-12)
            make.centerY.equalToSuperview()
        }
        
        // 列表视图约束
        tableView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }

    /// 设置事件绑定
    override func setupBindings() {
        // 省份按钮点击事件
        provinceButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.locationSelectionViewDidTapProvinceButton(self)
            }
            .store(in: &cancellables)
        
        // 城市按钮点击事件
        cityButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.locationSelectionViewDidTapCityButton(self)
            }
            .store(in: &cancellables)
    }

    // MARK: - Public Methods

    /// 更新地区数据
    func updateRegionData(_ regions: [RegionListItemModel], groupedRegions: [String: [RegionListItemModel]], sortedSectionKeys: [String]) {
        self.groupedRegions = groupedRegions
        self.sortedSectionKeys = sortedSectionKeys
        tableView.reloadData()
    }
    
    /// 重置为省份选择状态
    func resetToProvinceSelection() {
        isProvinceSelection = true
        provinceButton.setTitle("选择省份", for: .normal)
        provinceButton.isSelected = false
        cityButton.isSelected = false
        cityButton.setTitle("请选择城市", for: .normal)
    }

    /// 切换到省份选择
    func switchToProvinceSelection() {
        isProvinceSelection = true
        cityButton.setTitle("请选择城市", for: .normal)
        cityButton.isSelected = false
    }

    /// 切换到城市选择
    func switchToCitySelection() {
        isProvinceSelection = false
    }
    
    /// 显示城市选择按钮
    func showCitySelection() {
        cityButton.setTitle("请选择城市", for: .normal)
        cityButton.isSelected = false
    }
    
    /// 更新省份选择
    func updateProvinceSelection(_ provinceName: String) {
        provinceButton.setTitle(provinceName, for: .normal)
        provinceButton.isSelected = true
        // 显示城市选择按钮
        showCitySelection()
    }

    /// 更新城市选择
    func updateCitySelection(_ cityName: String) {
        cityButton.setTitle(cityName, for: .normal)
        cityButton.isSelected = true
    }

    /// 判断当前是否显示省份列表
    func isShowingProvinceList() -> Bool {
        return isProvinceSelection
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate

extension LocationSelectionView: UITableViewDataSource, UITableViewDelegate {
    func numberOfSections(in tableView: UITableView) -> Int {
        return sortedSectionKeys.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let sectionKey = sortedSectionKeys[section]
        return groupedRegions[sectionKey]?.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "LocationTableViewCell", for: indexPath) as! LocationTableViewCell
        
        let sectionKey = sortedSectionKeys[indexPath.section]
        guard let regions = groupedRegions[sectionKey], indexPath.row < regions.count else {
            return cell
        }
        
        let region = regions[indexPath.row]
        let isSelected = delegate?.locationSelectionView(self, isRegionSelected: region) ?? false
        let sectionTitle = indexPath.row == 0 ? sectionKey : ""
        
        cell.configure(with: region, isSelected: isSelected, sectionTitle: sectionTitle)
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let sectionKey = sortedSectionKeys[indexPath.section]
        guard let regions = groupedRegions[sectionKey], indexPath.row < regions.count else {
            return
        }
        
        let region = regions[indexPath.row]
        delegate?.locationSelectionView(self, didSelectRegion: region)
        
        tableView.reloadData()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 44
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return nil
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 0.01
    }
    func tableView(_ tableView: UITableView,viewForFooterInSection  section: Int) -> UIView? {
        return nil
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0.01
    }
}
