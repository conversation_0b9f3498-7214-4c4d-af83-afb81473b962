//
//  WalletRechargeController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine
import SnapKit

/// 充值控制器
class WalletRechargeController: BaseViewController {
    
    // MARK: - Properties
    
    /// 钱包ViewModel
    private let viewModel = WalletViewModel()
    
    // MARK: - UI Components
    
    /// 主滚动视图
    private lazy var scrollView = UIScrollView().then {
        $0.backgroundColor = .clear
        $0.showsVerticalScrollIndicator = false
    }
    
    /// 内容容器视图
    private lazy var contentView = UIView()
    
    /// 支付方式选择视图
    private lazy var paymentMethodView = WalletPaymentMethodView().then {
        $0.delegate = self
        $0.titleText = "选择充值方式"
    }
    
    /// 金额输入视图
    private lazy var amountInputView = WalletAmountInputView().then {
        $0.delegate = self
        $0.titleText = "充值金额"
        $0.configureAvailableAmount("", showAllButton: false)
    }
    
    /// 底部工具栏
    private lazy var bottomToolBar = BaseTabToolBar().then {
        $0.delegate = self
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "充值"
        configUI()
        configLayout()
        setupBindings()
        configureBottomToolBar()
    }
    
    // MARK: - UI Configuration
    
    /// UI配置
    override func configUI() {
        
        // 添加子视图
        view.addSubview(scrollView)
        view.addSubview(bottomToolBar)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(paymentMethodView)
        contentView.addSubview(amountInputView)
    }
    
    /// UI布局
    override func configLayout() {
        // 底部工具栏布局
        bottomToolBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
        
        // 主滚动视图布局
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomToolBar.snp.top)
        }
        
        // 内容容器视图布局
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        // 支付方式选择视图布局
        paymentMethodView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.right.equalToSuperview()
        }
        
        // 金额输入视图布局
        amountInputView.snp.makeConstraints { make in
            make.top.equalTo(paymentMethodView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    /// 设置数据绑定
    override func setupBindings() {
        // 设置自定义视图的事件绑定
        paymentMethodView.setupBindings()
        amountInputView.setupBindings()
        
        // 监听充值结果
        viewModel.$rechargeResult
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] message in
                self?.showSuccessAlert(message)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods
    
    /// 配置底部工具栏
    private func configureBottomToolBar() {
        bottomToolBar.configureLeftMultipleRightFull(
            leftItems: [],
            rightButtonTitle: "确认"
        )
    }
    
    /// 提交充值申请
    private func submitRechargeRequest() {
        let amountText = amountInputView.getAmount()
        
        // 验证输入
        guard !amountText.isEmpty, let amount = Float(amountText), amount > 0 else {
            showErrorAlert("请输入有效的充值金额")
            return
        }
        
        // 获取选中的支付方式
        let paymentMethod = paymentMethodView.getSelectedMethod()
        
        // 提交充值申请
        viewModel.submitRechargeRequest(amount: amount, paymentMethod: paymentMethod.title)
    }
    
    /// 显示成功提示
    private func showSuccessAlert(_ message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            // 返回上一页
            self?.navigationController?.popViewController(animated: true)
        })
        present(alert, animated: true)
    }
    
    /// 显示错误提示
    override func showErrorAlert(_ message: String) {
        let alert = UIAlertController(title: "错误", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - WalletPaymentMethodViewDelegate

extension WalletRechargeController: WalletPaymentMethodViewDelegate {
    /// 支付方式选择改变
    func walletPaymentMethodView(_ view: WalletPaymentMethodView, didSelectMethod method: WalletPaymentMethodType) {
        print("选择了支付方式: \(method.title)")
    }
}

// MARK: - WalletAmountInputViewDelegate

extension WalletRechargeController: WalletAmountInputViewDelegate {
    /// 金额输入改变
    func walletAmountInputView(_ view: WalletAmountInputView, didChangeAmount amount: String) {
        // 可以在这里添加实时验证逻辑
        print("金额输入改变: \(amount)")
    }
    
    /// 全部提现按钮点击（充值页面不显示此按钮）
    func walletAmountInputViewDidTapAllAmount(_ view: WalletAmountInputView) {
        // 充值页面不需要此功能
    }
}

// MARK: - TabToolBarDelegate

extension WalletRechargeController: TabToolBarDelegate {
    /// 右侧按钮点击事件
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 确认按钮点击
        submitRechargeRequest()
    }
}
