//
//  WalletDateSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine
import SwiftDate

/// 日期选择控制器
class WalletDateSelectController: BasePresentController {

    // MARK: - Properties

    /// 选择完成回调
    let selectionCompletedPublisher = PassthroughSubject<String, Never>()

    /// 当前选中的日期
    private var selectedDate: Date

    // MARK: - UI Components

    /// 自定义日期选择器
    private lazy var datePicker: CustomDatePicker = {
        let config = CustomDatePickerConfig(
            yearRange: .recent(months: 24),
            showYearSuffix: true,
            showMonthSuffix: true,
            needValidation: true
        )
        return CustomDatePicker(config: config)
    }()
    
    // MARK: - 初始化

    init(currentMonth: String) {
        // 使用SwiftDate解析当前月份
        if let date = currentMonth.toDate("yyyy-MM")?.date {
            self.selectedDate = date
        } else {
            self.selectedDate = Date()
        }
        super.init()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle

    override var presentationHeight: CGFloat {
        return 400
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        setupInitialDate()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "选择月份", bottomTitle: "确认")

        // 添加自定义日期选择器
        contentView.addSubview(datePicker)
        datePicker.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(200)
        }
    }
    
    // MARK: - Private Methods

    /// 设置初始日期
    private func setupInitialDate() {
        let dateString = String(format: "%04d-%02d", selectedDate.year, selectedDate.month)
        datePicker.setSelectedDate(dateString: dateString)
    }
    
    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        // 监听日期选择变化
        datePicker.dateSelectedPublisher
            .sink { [weak self] (year, month) in
                // 可以在这里处理日期变化
            }
            .store(in: &cancellables)

        // 确认按钮
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.confirmSelection()
            }
            .store(in: &cancellables)
    }

    /// 确认选择
    private func confirmSelection() {
        // 获取选中的月份字符串
        let monthString = datePicker.getSelectedDateString()

        // 发送选择完成事件
        selectionCompletedPublisher.send(monthString)

        // 关闭弹窗
        dismiss(animated: true)
    }
}
