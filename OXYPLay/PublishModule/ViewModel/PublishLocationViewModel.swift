//
//  PublishLocationViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//

import Foundation
import Combine

class PublishLocationViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 雪场地区列表
    @Published var regionList: [SkiRegionModel] = []
    
    /// 当前选中的地区
    @Published var selectedRegion: SkiRegionModel?
    
    /// 当前选中的雪场
    @Published var selectedVenue: SkiVenueModel?
    
    // MARK: - Public Methods
    
    /// 获取雪场地区列表
    func fetchRegionVenueList() {
        requestState = .loading        
        requestModel(AssemblyService.regionVenueList, type: SkiRegionResponse.self)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                guard let self = self else { return }
                
                switch completion {
                case .finished:
                    self.requestState = .success
                case .failure(let error):
                    self.requestState = .failure(self.handleError(error))
                    self.regionList = []
                }
            } receiveValue: { [weak self] response in
                guard let self = self else { return }
                self.regionList = response.regions
            }
            .store(in: &cancellables)
    }
    
    /// 设置选中的地区
    func setSelectedRegion(_ region: SkiRegionModel) {
        selectedRegion = region
        selectedVenue = nil // 重置雪场选择
    }
    
    /// 设置选中的雪场
    func setSelectedVenue(_ venue: SkiVenueModel) {
        selectedVenue = venue
    }
    
    /// 获取选中的位置信息
    func getSelectedLocationInfo() -> SelectedLocationInfo? {
        guard let region = selectedRegion else { return nil }
        return SelectedLocationInfo(region: region, venue: selectedVenue)
    }
    
    // MARK: - Private Methods
    
    /// 处理网络错误
    private func handleError(_ error: NetworkError) -> String {
        switch error {
        case .networkError(let response):
            return response.message
        case .decodingError:
            return "数据解析失败"
        case .noConnection:
            return "网络连接失败"
        case .tokenExpired:
            return "登录已过期"
        case .tokenError:
            return "登录状态异常"
        }
    }
}
