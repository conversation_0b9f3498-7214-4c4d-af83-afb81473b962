//
//  PublishViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//

import UIKit
import Combine

class PublishViewModel: BaseViewModel {

    // MARK: - Published属性

    /// 已上传的文件URL数组
    @Published var uploadedFileUrls: [UploadFileResponse] = []

    /// 上传状态
    @Published var uploadState: RequestState = .idle

    /// 发布状态
    @Published var publishState: RequestState = .idle

    /// 发布成功的帖子ID
    @Published var publishedPostId: String = ""

    /// 草稿保存状态
    @Published var draftSaveState: RequestState = .idle

    /// 草稿检查状态
    @Published var draftCheckState: RequestState = .idle

    /// 现有草稿信息
    @Published var existingDraft: CheckDraftResponse?

    // MARK: - 私有属性

    /// 当前上传任务的取消令牌
    private var currentUploadCancellable: AnyCancellable?

    /// 当前发布任务的取消令牌
    private var currentPublishCancellable: AnyCancellable?

}
extension PublishViewModel{
    
    // MARK: - 公共方法

    /// 上传图片列表（使用flatMap确保顺序，任一失败则整体失败）
    /// - Parameter images: 要上传的图片数组
    func uploadImages(_ images: [UIImage]) {
        guard !images.isEmpty else {
            uploadState = .failure("没有图片需要上传")
            return
        }

        // 取消之前的上传任务
        currentUploadCancellable?.cancel()

        // 重置状态
        uploadState = .loading
        uploadedFileUrls.removeAll()

        // 使用flatMap确保串行上传，保持顺序
        currentUploadCancellable = uploadImagesWithFlatMap(images)
    }

    /// 上传视频文件
    /// - Parameter videoURL: 视频文件URL
    func uploadVideo(_ videoURL: URL) {
        // 取消之前的上传任务
        currentUploadCancellable?.cancel()

        // 重置状态
        uploadState = .loading
        uploadedFileUrls.removeAll()

        // 执行视频上传
        currentUploadCancellable = uploadFile(videoURL)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        self.uploadState = .failure(self.handleUploadError(error))
                    }
                },
                receiveValue: { [weak self] uploadResponse in
                    guard let self = self else { return }

                    self.uploadedFileUrls.append(uploadResponse)
                    self.uploadState = .success
                }
            )
    }

    // MARK: - 私有方法

    /// 使用flatMap串行上传图片，确保顺序一致
    /// - Parameter images: 图片数组
    /// - Returns: 可取消的订阅
    private func uploadImagesWithFlatMap(_ images: [UIImage]) -> AnyCancellable {
        // 创建带索引的图片数组，确保顺序
        let indexedImages = images.enumerated().map { (index: $0, image: $1) }

        return Publishers.Sequence(sequence: indexedImages)
            .flatMap(maxPublishers: .max(1)) { [weak self] indexedImage -> AnyPublisher<(Int, UploadFileResponse), NetworkError> in
                guard let self = self else {
                    return Fail(error: NetworkError.decodingError("ViewModel已释放"))
                        .eraseToAnyPublisher()
                }

                let fileName = "image_\(indexedImage.index + 1)_\(Int(Date().timeIntervalSince1970)).jpg"

                return self.uploadImageFile(indexedImage.image, fileName: fileName)
                    .map { response in
                        // 返回索引和响应，确保可以按顺序处理
                        return (indexedImage.index, response)
                    }
                    .eraseToAnyPublisher()
            }
            .collect() // 收集所有结果
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        // 任何一个图片上传失败，整体失败
                        self.uploadState = .failure(self.handleUploadError(error))
                        self.uploadedFileUrls.removeAll()
                    }
                },
                receiveValue: { [weak self] results in
                    guard let self = self else { return }

                    // 按索引排序，确保URL数组顺序与原图片数组一致
                    let sortedResults = results.sorted { $0.0 < $1.0 }
                    self.uploadedFileUrls = sortedResults.map { $0.1 }
                    self.uploadState = .success
                }
            )
    }

    /// 上传图片文件
    /// - Parameters:
    ///   - image: 要上传的图片
    ///   - fileName: 文件名
    /// - Returns: 上传结果的Publisher
    private func uploadImageFile(_ image: UIImage, fileName: String) -> AnyPublisher<UploadFileResponse, NetworkError> {
        return requestModel(UploadService.uploadImage(image, fileName: fileName, mimeType: "image/jpeg"), type: UploadFileResponse.self)
    }

    /// 上传视频文件
    /// - Parameter videoURL: 视频文件URL
    /// - Returns: 上传结果的Publisher
    private func uploadFile(_ videoURL: URL) -> AnyPublisher<UploadFileResponse, NetworkError> {
        return requestModel(UploadService.uploadFile(videoURL), type: UploadFileResponse.self)
    }

    /// 取消当前上传任务
    func cancelUpload() {
        currentUploadCancellable?.cancel()
        uploadState = .idle
        uploadedFileUrls.removeAll()
    }


    /// 检查是否有上传任务正在进行
    /// - Returns: 是否正在上传
    func isUploading() -> Bool {
        switch uploadState {
        case .loading:
            return true
        default:
            return false
        }
    }

    /// 处理上传错误
    /// - Parameter error: 网络错误
    /// - Returns: 错误信息
    private func handleUploadError(_ error: NetworkError) -> String {
        switch error {
        case .networkError(let response):
            return response.message
        case .decodingError:
            return "文件上传失败，请重试"
        case .noConnection:
            return "网络连接失败，请检查网络设置"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .tokenError:
            return "登录状态异常，请重新登录"
        }
    }

    /// 发布服务帖子
    /// - Parameters:
    ///   - request: 发布请求参数
    ///   - selectedUsers: 选中的用户列表（用于黑白名单）
    func publishServicePost(_ request: CreateServicePostRequest, selectedUsers: [MineAddFriendItemModel] = []) {
        // 取消之前的发布任务
        currentPublishCancellable?.cancel()

        publishState = .loading

        // 创建帖子
        currentPublishCancellable = createServicePost(request)
            .flatMap { [weak self] response -> AnyPublisher<CreateServicePostResponse, NetworkError> in
                guard let self = self else {
                    return Fail(error: NetworkError.decodingError("ViewModel已释放"))
                        .eraseToAnyPublisher()
                }

                self.publishedPostId = response.id

                // 如果需要设置可见性，继续调用相关接口
                if request.visibility == 4 || request.visibility == 5, !selectedUsers.isEmpty {
                    let userIds = selectedUsers.map { $0.id }
                    return self.setPostVisibility(
                        postId: response.id,
                        type: request.type,
                        visibility: request.visibility,
                        userIds: userIds
                    )
                    .map { _ in response } // 返回原始响应
                    .eraseToAnyPublisher()
                } else {
                    return Just(response)
                        .setFailureType(to: NetworkError.self)
                        .eraseToAnyPublisher()
                }
            }
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        self.publishState = .failure(self.handleUploadError(error))
                    }
                },
                receiveValue: { [weak self] response in
                    guard let self = self else { return }

                    self.publishedPostId = response.id
                    self.publishState = .success
                }
            )
    }

    // MARK: - 私有发布方法

    /// 创建服务帖子
    private func createServicePost(_ request: CreateServicePostRequest) -> AnyPublisher<CreateServicePostResponse, NetworkError> {
        let params = RequestParameters(request.toDictionary() ?? [:])
        return requestModel(AssemblyService.create(params: params), type: CreateServicePostResponse.self)
    }

    /// 设置帖子可见性
    private func setPostVisibility(postId: String, type: Int, visibility: Int, userIds: [String]) -> AnyPublisher<ResponseModel, NetworkError> {
        if visibility == 4 { // 不给谁看
            let visibilityRequest = VisibilityRequest(
                type: type,
                post_id: postId,
                block_user_ids: userIds
            )
            let params = RequestParameters(visibilityRequest.toDictionary() ?? [:])
            return request(AssemblyService.visibilityBlock(params: params))
        } else if visibility == 5 { // 只给谁看
            let visibilityRequest = VisibilityRequest(
                type: type,
                post_id: postId,
                allow_user_ids: userIds
            )
            let params = RequestParameters(visibilityRequest.toDictionary() ?? [:])
            return request(AssemblyService.visibilityWhitelist(params: params))
        } else {
            return Just(ResponseModel(code: 200, message: "无需设置可见性"))
                .setFailureType(to: NetworkError.self)
                .eraseToAnyPublisher()
        }
    }

    /// 检查是否存在草稿
    func checkExistingDraft() {
        draftCheckState = .loading

        let params = RequestParameters([:])

        requestModel(AssemblyService.check(params: params), type: CheckDraftResponse.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        self.draftCheckState = .failure(self.handleUploadError(error))
                        self.existingDraft = nil
                    } else {
                        self.draftCheckState = .success
                    }
                },
                receiveValue: { [weak self] draft in
                    guard let self = self else { return }
                    self.existingDraft = draft
                }
            )
            .store(in: &cancellables)
    }

    /// 保存草稿
    /// - Parameter request: 草稿请求参数
    func saveDraft(_ request: CreateServicePostRequest) {
        draftSaveState = .loading

        let params = RequestParameters(request.toDictionary() ?? [:])

        requestModel(AssemblyService.saveDraft(params: params), type: SaveDraftResponse.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        self.draftSaveState = .failure(self.handleUploadError(error))
                    } else {
                        self.draftSaveState = .success
                    }
                },
                receiveValue: { [weak self] response in
                    guard let self = self else { return }
                    print("草稿保存成功，ID: \(response.id)")
                }
            )
            .store(in: &cancellables)
    }
}
