//
//  PublishNoteModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//

import UIKit
import SmartCodable

/// 发布笔记模型
struct PublishNoteModel: SmartCodable {
    /// 标题
    var title: String = ""
    
    /// 内容
    var content: String = ""
    
    /// 图片URL数组
    var imageUrls: [String] = []
    
    /// 分类
    var category: String = ""
    
    /// 位置
    var location: String = ""
    
    /// 可见性
    var visibility: String = "全部可见"
    
    /// 价格
    var price: String?
    
    /// 数量
    var quantity: String?
    
    /// 发布时间
    var publishTime: String?
    
    /// 用户ID
    var userId: String?
    
    
}

/// 发布笔记响应模型
struct PublishNoteResponse: SmartCodable {
    /// 是否成功
    var success: Bool = false

    /// 错误信息
    var message: String?

    /// 笔记ID
    var noteId: String?
}

/// 上传文件响应模型
struct UploadFileResponse: SmartCodable {
    /// 上传后的文件URL（CDN域名）
    var url: String = ""

    /// 文件类型：image 或 video
    var type: String = ""

    /// 图片宽度（仅图片时返回）
    var width: Int?

    /// 图片高度（仅图片时返回）
    var height: Int?
}

/// 创建服务帖子请求模型
struct CreateServicePostRequest: SmartCodable {
    /// 帖子类型，取值范围：1~8
    var type: Int = 2

    /// 帖子标题
    var title: String = ""

    /// 内容描述
    var description: String = ""

    /// 图片列表
    var img_urls: [UploadFileResponse] = []

    /// 城市信息
    var location: String = ""

    /// 联系方式说明
    var contact_info: String?

    /// 售价
    var price: String?

    /// 原价
    var origin_price: String?

    /// 数量，默认 1
    var quantity: Int = 1

    /// 配送方式：1包邮 2买家支付 3无需邮寄 4买家自提
    var postage_type: Int?

    /// 状态：1=发布中，2=下架，3=成交，4=草稿
    var status: Int = 1

    /// 可见性：1公开 2互关好友可见 3仅自己可见 4不给谁看 5只给谁看
    var visibility: Int = 1

    /// 屏蔽用户 ID 数组，定向不可见
    var block_user_ids: [String]?
}

/// 创建服务帖子响应模型
struct CreateServicePostResponse: SmartCodable {
    /// 新创建帖子的 ID
    var id: String = ""
}

/// 可见性设置请求模型
struct VisibilityRequest: SmartCodable {
    /// 帖子类型编号
    var type: Int = 2

    /// 帖子 ID
    var post_id: String = ""

    /// 用户 ID 数组
    var block_user_ids: [String]?
    var allow_user_ids: [String]?
}


/// 保存草稿响应模型
struct SaveDraftResponse: SmartCodable {
    /// 草稿对应的帖子 ID
    var id: Int = 0
}

/// 检查草稿响应模型
struct CheckDraftResponse: SmartCodable {
    /// 草稿帖子的 ID
    var id: String = ""

    /// 草稿类型（2~8）
    var type: Int = 2

    /// 草稿标题
    var title: String = ""

    /// 草稿创建时间
    var created_at: String = ""
}
