//
//  PublishLocationModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//

import Foundation

/// 雪场地区响应模型
struct SkiRegionResponse: SmartCodable {
    var regions: [SkiRegionModel] = []
}

/// 雪场地区模型
struct SkiRegionModel: SmartCodable {
    /// 城市名称（展示用）
    var name: String = ""
    
    /// 城市值（提交用）
    var value: String = ""
    
    /// 雪场列表
    var venues: [SkiVenueModel] = []
}

/// 雪场模型
struct SkiVenueModel: SmartCodable {
    /// 雪场名称（展示用）
    var name: String = ""
    
    /// 雪场值（提交用）
    var value: String = ""
}

/// 选中的位置信息
struct SelectedLocationInfo {
    let region: SkiRegionModel
    let venue: SkiVenueModel?
    
    /// 获取显示文本
    var displayText: String {
        if let venue = venue {
            return "\(region.name)-\(venue.name)"
        } else {
            return region.name
        }
    }
    
    /// 获取提交值
    var submitValue: String {
        if let venue = venue {
            return "\(region.value)-\(venue.value)"
        } else {
            return region.value
        }
    }
}
