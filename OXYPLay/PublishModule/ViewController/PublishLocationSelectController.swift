//
//  PublishLocationSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//

import UIKit
import Combine

class PublishLocationSelectController: BaseViewController {
    
    // MARK: - Properties
    
    private let viewModel = PublishLocationViewModel()
    
    /// 位置选择完成发布者
    let locationSelectedPublisher = PassthroughSubject<SelectedLocationInfo, Never>()
    
    // 当前选择状态
    private var currentSelectionLevel: SelectionLevel = .region
    
    // MARK: - UI Components
    
    // 顶部选中区域显示
    private lazy var selectedLocationView: UIStackView = {
        let view = UIStackView()
        view.spacing = 0
        view.axis = .vertical
        return view
    }()
    
    // 地区选择项
    private lazy var regionSelectionView: LocationSelectionItemView = {
        let view = LocationSelectionItemView()
        view.configure(title: "选择城市", isSelected: false, type: 0)
        view.onTap = { [weak self] in
            self?.regionButtonTapped()
        }
        return view
    }()
    
    // 雪场选择项
    private lazy var venueSelectionView: LocationSelectionItemView = {
        let view = LocationSelectionItemView()
        view.configure(title: "请选择雪场", isSelected: false, type: 1)
        view.bottomLine.isHidden = true
        view.onTap = { [weak self] in
            self?.venueButtonTapped()
        }
        return view
    }()
    
    // 标题标签
    private lazy var sectionTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "选择城市"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = color_2B2C2F
        return label
    }()
    
    // 表格视图
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.backgroundColor = .white
        tv.layer.cornerRadius = 16
        tv.masksToBounds = true
        tv.separatorStyle = .none
        tv.showsVerticalScrollIndicator = false
        tv.delegate = self
        tv.dataSource = self
        tv.register(LocationTableViewCell.self, forCellReuseIdentifier: "LocationTableViewCell")
        return tv
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        loadInitialData()
    }
    
    // MARK: - Setup Methods
    
    override func configUI() {
        title = "选择位置"
        setupSubviews()
        setupConstraints()
    }
    
    private func setupSubviews() {
        view.addSubview(selectedLocationView)
        
        // 添加选择项视图
        selectedLocationView.addArrangedSubview(regionSelectionView)
        selectedLocationView.addArrangedSubview(venueSelectionView)
        
        view.addSubview(sectionTitleLabel)
        view.addSubview(tableView)
    }
    
    private func setupConstraints() {
        selectedLocationView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
        }
        
        regionSelectionView.snp.makeConstraints { make in
            make.height.equalTo(38)
        }
        
        venueSelectionView.snp.makeConstraints { make in
            make.height.equalTo(38)
        }
        
        sectionTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(selectedLocationView.snp.bottom).offset(14)
            make.left.equalTo(12)
            make.height.equalTo(16)
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(sectionTitleLabel.snp.bottom).offset(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalToSuperview()
        }
    }
    
    override func setupBindings() {
        // 监听地区数据变化
        viewModel.$regionList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] regions in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
        
        // 监听请求状态
        viewModel.$requestState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handleRequestState(state)
            }
            .store(in: &cancellables)
    }
    
    private func loadInitialData() {
        // 初始化UI状态
        venueSelectionView.isHidden = true
        currentSelectionLevel = .region
        
        // 加载地区数据
        viewModel.fetchRegionVenueList()
    }
    
    // MARK: - Button Actions
    
    @objc private func regionButtonTapped() {
        currentSelectionLevel = .region
        sectionTitleLabel.text = "选择城市"
        
        // 重置选择状态
        viewModel.setSelectedRegion(SkiRegionModel())
        regionSelectionView.updateSelectionState(isSelected: false, type: 0)
        venueSelectionView.isHidden = true
        
        tableView.reloadData()
    }
    
    @objc private func venueButtonTapped() {
        guard let selectedRegion = viewModel.selectedRegion else { return }
        
        currentSelectionLevel = .venue
        sectionTitleLabel.text = "选择雪场"
        
        // 重置雪场选择状态
        viewModel.setSelectedVenue(SkiVenueModel())
        venueSelectionView.updateSelectionState(isSelected: false, type: 1)
        
        tableView.reloadData()
    }
    
    // MARK: - Private Methods
    
    private func handleRequestState(_ state: RequestState) {
        switch state {
        case .loading:
            // 可以添加加载指示器
            break
        case .success:
            break
        case .failure(let message):
            MBProgressHUD.showError(message, in: view)
        case .idle:
            break
        }
    }
}

// MARK: - Selection Level Enum

private enum SelectionLevel {
    case region  // 选择地区
    case venue   // 选择雪场
}

// MARK: - UITableViewDataSource & UITableViewDelegate

extension PublishLocationSelectController: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch currentSelectionLevel {
        case .region:
            return viewModel.regionList.count
        case .venue:
            return viewModel.selectedRegion?.venues.count ?? 0
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "LocationTableViewCell", for: indexPath) as! LocationTableViewCell
        
        switch currentSelectionLevel {
        case .region:
            guard indexPath.row < viewModel.regionList.count else { return cell }
            let region = viewModel.regionList[indexPath.row]
            let isSelected = viewModel.selectedRegion?.value == region.value
            
            // 创建临时的RegionListItemModel用于cell配置
            var tempRegion = RegionListItemModel()
            tempRegion.name = region.name
            tempRegion.code = region.value
            
            cell.configure(with: tempRegion, isSelected: isSelected, sectionTitle: "")
            
        case .venue:
            guard let venues = viewModel.selectedRegion?.venues, indexPath.row < venues.count else { return cell }
            let venue = venues[indexPath.row]
            let isSelected = viewModel.selectedVenue?.value == venue.value
            
            // 创建临时的RegionListItemModel用于cell配置
            var tempVenue = RegionListItemModel()
            tempVenue.name = venue.name
            tempVenue.code = venue.value
            
            cell.configure(with: tempVenue, isSelected: isSelected, sectionTitle: "")
        }
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        switch currentSelectionLevel {
        case .region:
            guard indexPath.row < viewModel.regionList.count else { return }
            let selectedRegion = viewModel.regionList[indexPath.row]
            handleRegionSelection(region: selectedRegion)
            
        case .venue:
            guard let venues = viewModel.selectedRegion?.venues, indexPath.row < venues.count else { return }
            let selectedVenue = venues[indexPath.row]
            handleVenueSelection(venue: selectedVenue)
        }
        
        tableView.reloadData()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 38
    }
    
    // MARK: - Selection Handlers
    
    private func handleRegionSelection(region: SkiRegionModel) {
        viewModel.setSelectedRegion(region)
        
        // 更新地区选择项
        regionSelectionView.updateTitle(region.name)
        regionSelectionView.updateSelectionState(isSelected: true, type: 0)
        
        if !region.venues.isEmpty {
            // 有雪场，显示雪场选择
            venueSelectionView.configure(title: "请选择雪场", isSelected: false, type: 1)
            venueSelectionView.isHidden = false
            
            currentSelectionLevel = .venue
            sectionTitleLabel.text = "选择雪场"
        } else {
            // 没有雪场，直接完成选择
            if let locationInfo = viewModel.getSelectedLocationInfo() {
                locationSelectedPublisher.send(locationInfo)
                navigationController?.popViewController(animated: true)
            }
        }
    }
    
    private func handleVenueSelection(venue: SkiVenueModel) {
        viewModel.setSelectedVenue(venue)
        
        // 更新雪场选择项
        venueSelectionView.updateTitle(venue.name)
        venueSelectionView.updateSelectionState(isSelected: true, type: 1)
        
        // 完成选择，返回结果
        if let locationInfo = viewModel.getSelectedLocationInfo() {
            locationSelectedPublisher.send(locationInfo)
            navigationController?.popViewController(animated: true)
        }
    }
}
