//
//  PublishPostTypeSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//

import UIKit
import Combine

class PublishPostTypeSelectController: BasePresentController {

    // MARK: - Properties

    var selectListConfig: ListItemConfig?
    let selectCompletedPublisher = PassthroughSubject<ListItemConfig, Never>()

    lazy var listView = BaseListView().then {
        $0.delegate = self
    }

    override var presentationHeight: CGFloat? {
        return 450
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        configureListItems()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "请选择发货方式", bottomTitle: "确定")
        // 添加列表视图
        contentView.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.bottom.equalTo(0)
            make.left.right.equalToSuperview()
        }
    }

    /// 配置发货方式列表项
    private func configureListItems() {
        let items: [[ListItemConfig]] = [
            // 邮寄分组
            [
                ListItemConfig.singleSelect(
                    identifier: "包邮",
                    title: "包邮",
                    isSelected: selectListConfig?.data as? String == "1",
                    data: "1"
                ),
                ListItemConfig.singleSelect(
                    identifier: "一口价（买家支付）",
                    title: "一口价（买家支付）",
                    isSelected: selectListConfig?.data as? String == "2",
                    data: "2"
                ),
                ListItemConfig.singleSelect(
                    identifier: "无需邮寄",
                    title: "无需邮寄",
                    isSelected: selectListConfig?.data as? String == "3",
                    data: "3"
                )
            ],
            // 自提分组
            [
                ListItemConfig.singleSelect(
                    identifier: "买家自提",
                    title: "买家自提",
                    isSelected: selectListConfig?.data as? String == "4",
                    data: "4"
                )
            ]
        ]

        let sectionTitles: [String?] = ["邮寄", "自提"]

        // 设置列表项和分组标题
        listView.setItems(items, sectionTitles: sectionTitles)
    }

    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let selectListConfig = self.selectListConfig else {
                    return
                }
                self.selectCompletedPublisher.send(selectListConfig)
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)
    }
}

// MARK: - BaseListViewDelegate

extension PublishPostTypeSelectController: BaseListViewDelegate {

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        self.selectListConfig = config

        // 更新列表中的选中状态
        configureListItems()
    }
}
