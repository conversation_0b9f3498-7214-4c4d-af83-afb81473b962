//
//  PublishPreviewController.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/31.
//

import UIKit

class PublishPreviewController: BaseViewController {

    // MARK: - Properties

    private var previewData: HomeDetailModel?

    // 区域类型（只显示图片和信息）
    private enum SectionType: Int, CaseIterable {
        case images = 0
        case info
    }

    // MARK: - UI Components

    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .grouped)
        tv.backgroundColor = color_F3F6F7
        tv.showsVerticalScrollIndicator = false
        tv.delegate = self
        tv.dataSource = self
        tv.separatorStyle = .none
        tv.estimatedRowHeight = 100
        tv.rowHeight = UITableView.automaticDimension
        tv.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 60, right: 0)
        tv.backgroundColor = .clear
        // 注册Cell
        tv.register(PreviewDetailImageTableCell.self, forCellReuseIdentifier: "PreviewDetailImageTableCell")
        tv.register(DetailInfoTableCell.self, forCellReuseIdentifier: "DetailInfoTableCell")
        return tv
    }()

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
    }

    // MARK: - Setup Methods

    override func configUI() {
        title = "预览"
        view.backgroundColor = color_F3F6F7

        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }

    // MARK: - Public Methods

    /// 配置预览数据
    /// - Parameter detailModel: 详情数据模型
    func configurePreview(with detailModel: HomeDetailModel) {
        self.previewData = detailModel
        tableView.reloadData()
    }

    /// 从表单数据创建预览数据
    /// - Parameters:
    ///   - formData: 表单数据
    ///   - images: 图片数组
    ///   - noteType: 笔记类型
    ///   - location: 位置信息
    ///   - price: 价格
    ///   - postageType: 发货方式
    ///   - visibility: 可见性
    func configurePreviewFromForm(
        formData: [String: Any],
        images: [UIImage],
        noteType: String,
        location: String,
        price: String?,
        postageType: String,
        visibility: String
    ) {
        // 创建预览用的HomeDetailModel
        var detailModel = HomeDetailModel()
        detailModel.title = formData["title"] as? String ?? ""
        detailModel.description = formData["description"] as? String ?? ""
        detailModel.location = location
        detailModel.createdAt = "刚刚"
        detailModel.type_text = noteType

        // 转换图片为URL格式（预览时使用本地图片）
        detailModel.imgUrls = images.enumerated().map { index, _ in
            "preview_image_\(index)" // 预览时的占位符URL
        }

        // 设置价格信息
        if let priceValue = price, !priceValue.isEmpty {
            detailModel.price = priceValue
        }

        // 存储本地图片用于预览显示
        PreviewImageCache.shared.setImages(images)

        self.previewData = detailModel
        tableView.reloadData()
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate

extension PublishPreviewController: UITableViewDataSource, UITableViewDelegate {

    func numberOfSections(in tableView: UITableView) -> Int {
        return SectionType.allCases.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard previewData != nil else { return 0 }
        return 1
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let detailData = previewData else {
            return UITableViewCell()
        }

        guard let sectionType = SectionType(rawValue: indexPath.section) else {
            return UITableViewCell()
        }

        switch sectionType {
        case .images:
            let cell = tableView.dequeueReusableCell(withIdentifier: "PreviewDetailImageTableCell", for: indexPath) as! PreviewDetailImageTableCell
            // 使用本地图片进行预览
            let localImages = PreviewImageCache.shared.getAllImages()
            if !localImages.isEmpty {
                cell.configureWithLocalImages(localImages, model: detailData)
            } else {
                cell.configure(with: detailData)
            }
            return cell

        case .info:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailInfoTableCell", for: indexPath) as! DetailInfoTableCell
            cell.configure(with: detailData)
            return cell
        }
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 0.01
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 12
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return UIView()
    }

    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return UIView()
    }
}

// MARK: - 预览图片缓存

/// 预览图片缓存单例，用于在预览时显示本地图片
class PreviewImageCache {
    static let shared = PreviewImageCache()

    private var cachedImages: [UIImage] = []

    private init() {}

    /// 设置预览图片
    func setImages(_ images: [UIImage]) {
        cachedImages = images
    }

    /// 获取指定索引的预览图片
    func getImage(at index: Int) -> UIImage? {
        guard index >= 0 && index < cachedImages.count else { return nil }
        return cachedImages[index]
    }

    /// 获取所有预览图片
    func getAllImages() -> [UIImage] {
        return cachedImages
    }

    /// 清除缓存
    func clearCache() {
        cachedImages.removeAll()
    }
}
