//
//  PublishNoteTypeSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//

class PublishNoteTypeSelectController: BasePresentController {

    // MARK: - Properties

    var selectListConfig:ListItemConfig!
    let selectCompletedPublisher = PassthroughSubject<(ListItemConfig), Never>()
    lazy var listView = BaseListView().then {
        $0.delegate = self
    }
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        configureListItems()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "请选择笔记类别（唯一）", bottomTitle: "确定")
        // 添加列表视图
        contentView.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.bottom.equalTo(0)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(44*6)
            make.bottom.equalToSuperview()
        }
    }

    private func configureListItems() {
        let items: [[ListItemConfig]] = [
            [ListItemConfig.singleSelect(
                identifier: "publish_find_people",
                title: "找人",
                iconString: "publish_find_people",
                isSelected: selectListConfig.data as! String == "2",
                subTitle: "教练/雪搭子/陪滑/学员",
                data: "2"
            ),
             ListItemConfig.singleSelect(
                 identifier: "publish_photography",
                 title: "摄影/摄像",
                 iconString: "publish_photography",
                 isSelected: selectListConfig.data as! String == "3",
                 subTitle: "雪场摄影师/摄像师",
                 data: "3"
             ),
              ListItemConfig.singleSelect(
                  identifier: "publish_housing",
                  title: "找房",
                  iconString: "publish_housing",
                  isSelected: selectListConfig.data as! String == "4",
                  subTitle: "房东/房客/拼房间",
                  data: "4"
              ),
               ListItemConfig.singleSelect(
                   identifier: "publish_secondhand",
                   title: "找闲置",
                   iconString: "publish_secondhand",
                   isSelected: selectListConfig.data as! String == "5",
                   subTitle: "雪票/雪板/固定器等装备",
                   data: "5"
               ),
                ListItemConfig.singleSelect(
                    identifier: "publish_vehicle",
                    title: "找车",
                    iconString: "publish_vehicle",
                    isSelected: selectListConfig.data as! String == "6",
                    subTitle: "拼车/代驾/顺风车/专车",
                    data: "6"
                ),
                 ListItemConfig.singleSelect(
                     identifier: "publish_post",
                     title: "有话说",
                     iconString: "publish_post",
                     isSelected: selectListConfig.data as! String == "7",
                     subTitle: "吐槽/心得/成长记录/安利/种草",
                     data: "7"
                 ),
            ]
        ]
        
        // 设置列表项
        listView.setItems(items)
    }
    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self ,let selectListConfig = self.selectListConfig else {
                    return
                }
                self.selectCompletedPublisher.send(selectListConfig)
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)
    }

  
}
// MARK: - BaseListViewDelegate

extension PublishNoteTypeSelectController: BaseListViewDelegate {

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        self.selectListConfig = config
    }
    
}
