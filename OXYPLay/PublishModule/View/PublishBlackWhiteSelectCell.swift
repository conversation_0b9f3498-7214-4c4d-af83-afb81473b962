//
//   PublishBlackWhiteSelectCell.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/31.
//

class PublishBlackWhiteSelectCell: UITableViewCell {

    // MARK: - 属性

    var userModel: MineAddFriendItemModel? {
        didSet {
            updateUI()
        }
    }


    // MARK: - UI组件

    // 头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 18
        imageView.layer.masksToBounds = true
        imageView.backgroundColor = .random
        return imageView
    }()

    // 用户名
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.text = "hahhahaha"
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textColor = UIColor(hexString: "2B2C2F")
        label.numberOfLines = 1
        return label
    }()
   
    private lazy var selectButton: BaseButton = {
        let button = BaseButton()
        button.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        button.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        return button
    }()


    // MARK: - 初始化

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - UI设置

    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .white
        contentView.addSubview(avatarImageView)
        contentView.addSubview(usernameLabel)
        contentView.addSubview(selectButton)
        setupConstraints()

    }

    private func setupConstraints() {
        selectButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(12)
        }
        avatarImageView.snp.makeConstraints { make in
            make.width.height.equalTo(36)
            make.top.equalTo(12)
            make.bottom.equalTo(-12)
            make.left.equalTo(selectButton.snp.right).offset(12)
        }
        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.centerY.equalToSuperview()
            make.right.equalTo(12)
        }
    }

   


  

    // MARK: - 数据更新

    private func updateUI() {
        guard let userModel = userModel else { return }

        // 设置用户名
        usernameLabel.text = userModel.nickname
        avatarImageView.setImage(url: userModel.avatar)
        selectButton.isSelected = userModel.isSelect
    }


}

