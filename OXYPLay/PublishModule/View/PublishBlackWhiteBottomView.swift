//
//   PublishBlackWhiteBottomView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//
protocol PublishBlackWhiteBottomViewDelegate: AnyObject {
    func delete(_ userid: String)
    func okButtonClick()
}

class PublishBlackWhiteBottomView:BaseView{
    weak var delegate:PublishBlackWhiteBottomViewDelegate?
    lazy var stackView = UIStackView().then{
        $0.axis = .horizontal
        $0.spacing = 20
    }
    lazy var scrollView = UIScrollView()
    lazy var okButton = BaseButton().then{
        $0.backgroundColor = color_blue
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 12, weight: .medium)
        $0.isRounded = true
    }
    override func configUI() {
        addSubview(scrollView)
        scrollView.addSubview(stackView)
        addSubview(okButton)
        setupBindings()
        
    }
    override func configLayout() {
        okButton.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.right.equalTo(-12)
            make.width.equalTo(60)
            make.height.equalTo(28)
        }
        scrollView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalTo(12)
            make.height.equalTo(67)
            make.right.equalTo(okButton.snp.left).offset(-20)
        }
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(67)
        }
        
    }
    override func setupBindings() {
        okButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.okButtonClick()
            }
            .store(in: &cancellables)
        
    }
    func configList(list:[MineAddFriendItemModel]){
        okButton.setTitle("确定（\(list.count)）", for: .normal)
        stackView.subviews.forEach{$0.removeFromSuperview()}
        list.forEach { model in
            let view = PublishBlackWhiteBottomItemView()
          
            view.avatarImageView.setImage(url: model.avatar)
            view.usernameLabel.text = model.nickname
            view.deletButton.tapPublisher
                .sink { [weak self] _ in
                    guard let self = self else { return }
                    self.delegate?.delete(model.id)
                }
                .store(in: &cancellables)
            stackView.addArrangedSubview(view)
        }
    }
}
class PublishBlackWhiteBottomItemView:BaseView{
    // 头像
    lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 18
        imageView.layer.masksToBounds = true
        imageView.backgroundColor = .random
        return imageView
    }()

    // 用户名
    lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.text = "hahhahaha"
        label.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        label.textColor = UIColor(hexString: "2B2C2F")
        label.numberOfLines = 1
        label.textAlignment = .center
        return label
    }()
   
    lazy var deletButton: BaseButton = {
        let button = BaseButton()
        button.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        button.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        return button
    }()

    override func configUI() {
        addSubview(avatarImageView)
        addSubview(usernameLabel)
        addSubview(deletButton)

    }
    override func configLayout() {
        avatarImageView.snp.makeConstraints { make in
           
            make.centerX.equalToSuperview()
            make.width.height.equalTo(36)
            make.top.equalTo(8.5)

        }
        deletButton.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.top).offset(-5)
            make.centerX.equalTo(avatarImageView.snp.right)
            make.width.height.equalTo(16)
        
        }
        usernameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(6)
            make.left.right.equalToSuperview()
            make.width.greaterThanOrEqualTo(36)

        }
    }
}
