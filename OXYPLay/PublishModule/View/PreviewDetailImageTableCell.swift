//
//  PreviewDetailImageTableCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//

import UIKit
import SnapKit
import Then
import Kingfisher
import Combine

/// 预览专用的图片表格单元格，支持显示本地图片
class PreviewDetailImageTableCell: UITableViewCell {
    
    // MARK: - UI组件
    
    private lazy var imagesView = PreviewDetailProductImagesView()
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = color_F3F6F7
        
        contentView.addSubview(imagesView)
        imagesView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - 配置方法
    
    /// 使用HomeDetailModel配置（网络图片）
    func configure(with model: HomeDetailModel) {
        imagesView.configure(with: model)
    }
    
    /// 使用本地图片配置（预览时使用）
    func configureWithLocalImages(_ images: [UIImage], model: HomeDetailModel) {
        imagesView.configureWithLocalImages(images, model: model)
    }
}

/// 预览专用的产品图片视图，支持显示本地图片
class PreviewDetailProductImagesView: BaseView {
    
    // MARK: - 属性
    
    private var imageUrls: [String] = []
    private var localImages: [UIImage] = []
    private var isPreviewMode: Bool = false
    
    // MARK: - UI组件
    
    private lazy var containerView = UIView()
    
    private lazy var pageControl = UILabel().then {
        $0.backgroundColor = UIColor(hexString: "000000", transparency: 0.48)
        $0.textColor = .white
        $0.font = .systemFont(ofSize: 12, weight: .regular)
        $0.layer.cornerRadius = 12
        $0.layer.masksToBounds = true
        $0.textAlignment = .center
    }
    
    private lazy var tagLabel = UILabel().then {
        $0.backgroundColor = UIColor(hexString: "000000", transparency: 0.48)
        $0.textColor = .white
        $0.font = .systemFont(ofSize: 12, weight: .regular)
        $0.layer.cornerRadius = 12
        $0.layer.masksToBounds = true
        $0.textAlignment = .center
    }
    
    private lazy var locationButton = BaseButton().then {
        $0.backgroundColor = UIColor(hexString: "000000", transparency: 0.48)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 12, weight: .regular)
        $0.isRounded = true
        $0.setImage(UIImage(named: "assembly_location")?.withTintColor(.white), for: .normal)
        $0.spacing = 5
    }
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        
        let collection = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collection.backgroundColor = .white
        collection.showsHorizontalScrollIndicator = false
        collection.isPagingEnabled = true
        collection.delegate = self
        collection.dataSource = self
        collection.register(PreviewProductImageCell.self, forCellWithReuseIdentifier: "PreviewProductImageCell")
        return collection
    }()
    
    // MARK: - UI设置
    
    override func configUI() {
        backgroundColor = color_F3F6F7
        
        addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(kScreenWidth)
            make.bottom.equalToSuperview().priority(.low)
        }
        
        containerView.addSubview(pageControl)
        pageControl.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.bottom.equalTo(collectionView.snp.bottom).offset(-10)
            make.height.equalTo(24)
            make.width.equalTo(45)
        }
        
        containerView.addSubview(tagLabel)
        tagLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(12)
            make.height.equalTo(24)
            make.width.equalTo(45)
        }
        
        containerView.addSubview(locationButton)
        locationButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.bottom.equalTo(collectionView.snp.bottom).offset(-10)
            make.height.equalTo(24)
            make.width.equalTo(locationButton.intrinsicContentSize.width + 10)
        }
    }
    
    // MARK: - 配置方法
    
    /// 使用HomeDetailModel配置（网络图片）
    func configure(with model: HomeDetailModel) {
        isPreviewMode = false
        imageUrls = model.imgUrls
        localImages = []
        
        updateUI(with: model)
        collectionView.reloadData()
    }
    
    /// 使用本地图片配置（预览模式）
    func configureWithLocalImages(_ images: [UIImage], model: HomeDetailModel) {
        isPreviewMode = true
        localImages = images
        imageUrls = []
        
        updateUI(with: model)
        collectionView.reloadData()
    }
    
    private func updateUI(with model: HomeDetailModel) {
        let imageCount = isPreviewMode ? localImages.count : imageUrls.count
        
        if imageCount > 1 {
            pageControl.text = "1/\(imageCount)"
        } else {
            pageControl.text = "0"
        }
        
        tagLabel.text = model.type_text
        tagLabel.isHidden = model.type_text.isEmpty
        
        locationButton.setTitle(model.location, for: .normal)
        locationButton.snp.updateConstraints { make in
            make.width.equalTo(locationButton.intrinsicContentSize.width + 15)
        }
        locationButton.isHidden = model.location.isEmpty
        
        tagLabel.snp.updateConstraints { make in
            make.width.equalTo(tagLabel.intrinsicContentSize.width + 15)
        }
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDataSource

extension PreviewDetailProductImagesView: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return isPreviewMode ? localImages.count : imageUrls.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PreviewProductImageCell", for: indexPath) as! PreviewProductImageCell
        
        if isPreviewMode {
            // 预览模式：显示本地图片
            if indexPath.item < localImages.count {
                cell.configureWithLocalImage(localImages[indexPath.item])
            }
        } else {
            // 正常模式：显示网络图片
            if indexPath.item < imageUrls.count {
                cell.configure(with: imageUrls[indexPath.item], cornerRadius: 0)
            }
        }
        
        return cell
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let pageWidth = scrollView.frame.width
        let currentPage = Int((scrollView.contentOffset.x + pageWidth / 2) / pageWidth)
        let totalPages = isPreviewMode ? localImages.count : imageUrls.count
        pageControl.text = "\(currentPage + 1)/\(totalPages)"
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension PreviewDetailProductImagesView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: collectionView.frame.width, height: collectionView.frame.height)
    }
}

/// 预览专用的产品图片单元格，支持本地图片显示
class PreviewProductImageCell: UICollectionViewCell {
    
    private lazy var imageView = UIImageView().then {
        $0.contentMode = .scaleAspectFit
        $0.clipsToBounds = true
        $0.backgroundColor = .clear
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    /// 配置网络图片
    func configure(with imageUrl: String, cornerRadius: CGFloat = 12, isSelect: Bool = false) {
        imageView.setImage(url: imageUrl)
        imageView.layer.cornerRadius = cornerRadius
        imageView.layer.borderWidth = isSelect ? 1 : 0
    }
    
    /// 配置本地图片（预览模式）
    func configureWithLocalImage(_ image: UIImage) {
        imageView.image = image
        imageView.layer.cornerRadius = 0
        imageView.layer.borderWidth = 0
    }
}
