//
//  Untitled.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/30.
//

// MARK: - 标题输入列表项
class TitleInputItemView: ListItemView {
    
    // MARK: - UI组件
    
    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }

    lazy var starLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_red
        $0.text = "*"
    }

    lazy var titleTextField = UITextField().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.placeholder = "添加标题"
        $0.textAlignment = .right
        $0.returnKeyType = .next
    }
    
    lazy var separatorLine = UIView().then {
        $0.backgroundColor = color_2B2C2F04
    }
    
    // MARK: - 初始化方法
    
    override func configUI() {
        backgroundColor = .white
        addSubview(titleTextField)
        addSubview(separatorLine)
        addSubview(titleLabel)
        addSubview(starLabel)

        // 绑定文本变更事件
        titleTextField.textPublisher
            .sink { [weak self] text in
                self?.data = text
            }
            .store(in: &cancellables)
    }
    
    override func configLayout() {
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(12)
            make.bottom.equalTo(-12)
        }
        starLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel.snp.right).offset(4)
            make.centerY.equalTo(titleLabel)
        }
        titleTextField.snp.makeConstraints { make in
            make.left.equalTo(separatorLine)
            make.right.equalTo(-12)
            make.top.equalTo(0)
            make.bottom.equalTo(0)
            make.height.equalTo(42)
        }
        
        separatorLine.snp.makeConstraints { make in
            make.left.equalTo(90)
            make.right.equalTo(titleTextField)
            make.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
    }
    
    // MARK: - 配置方法
    
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
        
        titleTextField.placeholder = config.placeholder.isEmpty ? "请填写\(config.title)" : config.placeholder
        titleLabel.text = config.title
        starLabel.isHidden = !config.isRequired
        if let text = config.data as? String {
            titleTextField.text = text
            data = text
        }
    }
}
class InputItemView: ListItemView {
    
    // MARK: - UI组件
    
    lazy var titleTextField = UITextField().then {
        $0.font = UIFont.systemFont(ofSize: 16,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.placeholder = "添加标题"
        $0.returnKeyType = .next
    }
    
    lazy var separatorLine = UIView().then {
        $0.backgroundColor = color_2B2C2F04
    }
    
    // MARK: - 初始化方法
    
    override func configUI() {
        backgroundColor = .white
        addSubview(titleTextField)
        addSubview(separatorLine)
        
        // 绑定文本变更事件
        titleTextField.textPublisher
            .sink { [weak self] text in
                self?.data = text
            }
            .store(in: &cancellables)
    }
    
    override func configLayout() {
        titleTextField.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(0)
            make.bottom.equalTo(0)
            make.height.equalTo(42)
        }
        
        separatorLine.snp.makeConstraints { make in
            make.left.equalTo(titleTextField)
            make.right.equalTo(titleTextField)
            make.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
    }
    
    // MARK: - 配置方法
    
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
        
        titleTextField.placeholder = config.placeholder.isEmpty ? "请填写\(config.title)" : config.placeholder
        
        if let text = config.data as? String {
            titleTextField.text = text
            data = text
        }
    }
}
class InputIconItemView: ListItemView {
    
    // MARK: - UI组件
    lazy var iconImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFit
    }
    lazy var titleTextField = UITextField().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.returnKeyType = .next
        $0.keyboardType = .numberPad
        $0.textAlignment = .right
    }
    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }
    // MARK: - 初始化方法
    
    override func configUI() {
        backgroundColor = .white
        addSubview(iconImageView)
        addSubview(titleLabel)
        addSubview(titleTextField)
        
        // 绑定文本变更事件
        titleTextField.textPublisher
            .sink { [weak self] text in
                self?.data = text
            }
            .store(in: &cancellables)
    }
    
    override func configLayout() {
        self.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
        iconImageView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(10)
            make.centerY.equalToSuperview()
        }
        
        titleTextField.snp.makeConstraints { make in
            make.left.equalTo(titleLabel.snp.right).offset(12)
            make.right.equalTo(-12)
            make.top.equalTo(0)
            make.bottom.equalTo(0)
        }
    }
    
    // MARK: - 配置方法
    
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
        iconImageView.image = UIImage(named: config.iconString)
        titleLabel.text = config.title
        titleTextField.placeholder = config.placeholder.isEmpty ? "请填写\(config.title)" : config.placeholder
        if let text = config.data as? String {
            titleTextField.text = text
            data = text
        }
    }
}
class InputPriceItemView: ListItemView {
    private lazy var params = [String:Any]()
    // MARK: - UI组件
    lazy var contentView = UIView().then {
        $0.backgroundColor =  UIColor(hexString: "#788092", transparency: 0.08)
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }
    lazy var priceContentView = UIView()
    lazy var priceTextField = UITextField().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.returnKeyType = .next
        $0.keyboardType = .numberPad
        $0.textAlignment = .right
        $0.placeholder = "请填写出原价"
    }
    lazy var requiredLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 10,weight: .regular)
        $0.textColor = UIColor(hexString: "#FF0000", transparency: 0.48)
        $0.backgroundColor = UIColor(hexString: "#FF0000", transparency: 0.08)
        $0.textAlignment = .center
        $0.setPadding(horizontal: 3, vertical: 2)
        $0.text = "必填"
        $0.cornerRadius = 4
        $0.setContentHuggingPriority(.required, for: .horizontal) // 水平方向紧贴内容
    }
    lazy var priceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.text = "原价"
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }
    lazy var numberContentView = UIView()
    lazy var numberTextField = UITextField().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.returnKeyType = .next
        $0.keyboardType = .numberPad
        $0.textAlignment = .right
        $0.placeholder = "请填写出售数量"
    }
    lazy var numberLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.text = "出售数量"
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }
    // MARK: - 初始化方法
    
    override func configUI() {
        isHidden = true
        backgroundColor = .white
        addSubview(contentView)
        contentView.addSubview(priceContentView)
        contentView.addSubview(numberContentView)
        priceContentView.addSubview(priceLabel)
        priceContentView.addSubview(priceTextField)
        numberContentView.addSubview(numberLabel)
        numberContentView.addSubview(requiredLabel)
        numberContentView.addSubview(numberTextField)
        setupBindings()
    }
    override func setupBindings() {
        numberTextField.textPublisher
            .sink { [weak self] text in
                self?.params["quantity"] = text
                self?.data = self?.params
                print(self?.data)

            }
            .store(in: &cancellables)
        priceTextField.textPublisher
            .sink { [weak self] text in
                self?.params["origin_price"] = text
                self?.data = self?.params
                print(self?.data)
            }
            .store(in: &cancellables)
    }
    
    override func configLayout() {
        contentView.snp.makeConstraints { make in
            make.left.equalTo(40)
            make.top.equalTo(0)
            make.bottom.equalTo(-0)
            make.right.equalTo(-12)
        }
        numberContentView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        priceContentView.snp.makeConstraints { make in
            make.top.equalTo(numberContentView.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
            make.bottom.equalToSuperview()
        }
        numberLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
        }
        requiredLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(numberLabel.snp.right).offset(8)
        }
        numberTextField.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(requiredLabel.snp.right).offset(8)
        }
        priceLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
        }
        
        priceTextField.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(priceLabel.snp.right).offset(8)
        }
        
    }
    
    // MARK: - 配置方法
    
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
    }
}
// MARK: - 内容输入列表项

class ContentInputItemView: ListItemView {

    // MARK: - 属性

    /// 字数限制，默认100字
    var maxCharacterCount: Int = 100 {
        didSet {
            updateLimitLabel()
        }
    }

    /// 当前字数
    private var currentCharacterCount: Int = 0 {
        didSet {
            updateLimitLabel()
        }
    }

    // MARK: - Combine Publishers

    /// 文本内容变化的Publisher
    @Published var textContent: String = ""

    /// 字符数变化的Publisher
    @Published var characterCount: Int = 0

    /// 是否超过字数限制的Publisher
    @Published var isOverLimit: Bool = false

    /// @用户按钮点击事件的Subject
    private let mentionUserSubject = PassthroughSubject<Void, Never>()
    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.setContentHuggingPriority(.required, for: .horizontal)
        $0.text = "个人简介"
    }

    // MARK: - UI组件

    lazy var contentTextView = PlaceholderTextView().then {
        $0.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.isScrollEnabled = false
        $0.textContainerInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        $0.textContainer.lineFragmentPadding = 0
    }
    
    lazy var limitLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = color_2B2C2F40
        $0.text = "0/\(maxCharacterCount)"
        $0.textAlignment = .right
    }
    lazy var button = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setTitle("@用户", for: .normal)
        $0.horizontalPadding = 6
        $0.verticalPadding = 4
        $0.isRounded = true
        $0.backgroundColor = UIColor(hexString: "#788092", transparency: 0.08)
    }
    
    // MARK: - 初始化方法
    
    override func configUI() {
        backgroundColor = .white
        addSubview(contentTextView)
        addSubview(limitLabel)
        addSubview(button)
        // 设置代理
        contentTextView.delegate = self
        // 设置button点击事件
        setupBindings()
    }
    
    override func configLayout() {
        contentTextView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(12)
            make.bottom.equalTo(-36)
            make.height.equalTo(213)
        }
        
        limitLabel.snp.makeConstraints { make in
            make.right.bottom.equalTo(-12)
        }
        button.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.bottom.equalTo(-12)
        }
    }
    
    // MARK: - 配置方法

    override func configure(with config: ListItemConfig) {
        super.configure(with: config)

        contentTextView.placeholder = config.placeholder.isEmpty ? "请填写\(config.title)" : config.placeholder

        // 设置字数限制
        if let maxCount = config.maxLength, maxCount > 0 {
            maxCharacterCount = maxCount
        }

        if let text = config.data as? String {
            contentTextView.text = text
            data = text
            currentCharacterCount = text.count
        } else {
            currentCharacterCount = 0
        }
        if maxCharacterCount == 20 {
            button.isHidden = true
            self.addSubview(titleLabel)
            titleLabel.snp.makeConstraints { make in
                make.top.leading.equalTo(12)
            }
            contentTextView.snp.remakeConstraints { make in
                make.left.equalTo(12)
                make.right.equalTo(-12)
                make.top.equalTo(titleLabel.snp.bottom).offset(12)
                make.bottom.equalTo(-12)
                make.height.equalTo(80)
            }
        }
    }

    // MARK: - 私有方法

    /// 设置事件绑定
    override func setupBindings() {
        // 绑定@用户按钮点击事件
        button.tapPublisher
            .sink { [weak self] _ in
                self?.mentionUserSubject.send()
            }
            .store(in: &cancellables)
    }

    /// 更新字数限制标签
    private func updateLimitLabel() {
        limitLabel.text = "\(currentCharacterCount)/\(maxCharacterCount)"

        // 更新Published属性
        characterCount = currentCharacterCount
        isOverLimit = currentCharacterCount > maxCharacterCount

        // 根据字数变化颜色
        if currentCharacterCount > maxCharacterCount {
            limitLabel.textColor = UIColor.red
        } else if currentCharacterCount >= maxCharacterCount * 9 / 10 {
            // 接近限制时显示橙色警告
            limitLabel.textColor = UIColor.orange
        } else {
            limitLabel.textColor = color_2B2C2F40
        }
    }
}

// MARK: - UITextViewDelegate

extension ContentInputItemView: UITextViewDelegate {

    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        // 获取当前文本
        let currentText = textView.text ?? ""

        // 计算新文本
        guard let stringRange = Range(range, in: currentText) else { return false }
        let newText = currentText.replacingCharacters(in: stringRange, with: text)

        // 检查字数限制
        let newCharacterCount = newText.count

        // 如果超过限制，不允许输入
        if newCharacterCount > maxCharacterCount {
            return false
        }

        return true
    }

    func textViewDidChange(_ textView: UITextView) {
        let text = textView.text ?? ""
        currentCharacterCount = text.count
        data = text

        // 更新Published属性
        textContent = text
    }

    func textViewDidBeginEditing(_ textView: UITextView) {
        // 可以在这里添加开始编辑的逻辑
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        // 可以在这里添加结束编辑的逻辑
    }
    /// 获取文本内容变化的Publisher
    var textPublisher: AnyPublisher<String, Never> {
        return $textContent.eraseToAnyPublisher()
    }

    /// 获取字符数变化的Publisher
    var characterCountPublisher: AnyPublisher<Int, Never> {
        return $characterCount.eraseToAnyPublisher()
    }

    /// 获取是否超过限制的Publisher
    var isOverLimitPublisher: AnyPublisher<Bool, Never> {
        return $isOverLimit.eraseToAnyPublisher()
    }

    /// 获取文本和字符数的组合Publisher
    var textWithCountPublisher: AnyPublisher<(text: String, count: Int, isOverLimit: Bool), Never> {
        return Publishers.CombineLatest3($textContent, $characterCount, $isOverLimit)
            .map { (text: $0, count: $1, isOverLimit: $2) }
            .eraseToAnyPublisher()
    }

    /// 获取@用户按钮点击的Publisher
    var mentionUserPublisher: AnyPublisher<Void, Never> {
        return mentionUserSubject.eraseToAnyPublisher()
    }
}

