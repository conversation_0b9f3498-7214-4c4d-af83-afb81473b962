//
//  SingleSelectorItemView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/17.
//

class SingleSelectorItemView: ListSelectItemView {

    // MARK: - UI组件

    lazy var iconImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFit
    }

    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }

    lazy var valueLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.4)
        $0.textAlignment = .right
    }
    lazy var selectButton = UIButton().then {
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
    }

    // MARK: - 初始化方法

    override func configUI() {
        backgroundColor = .white
        addSubview(iconImageView)
        addSubview(titleLabel)
        addSubview(valueLabel)
        addSubview(selectButton)

    }

    override func configLayout() {
        self.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
        iconImageView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(10)
            make.centerY.equalToSuperview()
        }

        selectButton.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(14)
        }

        valueLabel.snp.makeConstraints { make in
            make.right.lessThanOrEqualTo(selectButton.snp.left).offset(-10)
            make.centerY.equalToSuperview()
            make.left.lessThanOrEqualTo(titleLabel.snp.right).offset(8)
        }

    }
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
        titleLabel.text = config.title
        valueLabel.text = config.subTitle
        selectButton.isSelected = config.isSelect

        // 更新选中状态的数据
        self.data = config.isSelect
        if config.iconString.isEmpty{
            titleLabel.snp.remakeConstraints { make in
                make.left.equalTo(12)
                make.centerY.equalToSuperview()
            }
        }else{
            iconImageView.image = UIImage(named: config.iconString)
        }
    }

    // MARK: - Override Methods

    override func getData() -> Any? {
        return selectButton.isSelected
    }

    /// 设置选中状态
    func setSelected(_ selected: Bool) {
        selectButton.isSelected = selected
        self.data = selected

        // 更新配置
        itemConfig?.isSelect = selected
    }

    /// 获取选中状态
    func isSelected() -> Bool {
        return selectButton.isSelected
    }
}
class StarSelectItemView: ListSelectItemView {
    
    // MARK: - UI组件
    
    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }

    lazy var starLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_red
        $0.text = "*"
    }

    lazy var valueLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.textAlignment = .right
  
    }
    lazy var arrowImageView = UIImageView().then {
        $0.image = UIImage(named: "baselist_arrow")
    }
    lazy var separatorLine = UIView().then {
        $0.backgroundColor = color_2B2C2F04
    }
    
    // MARK: - 初始化方法
    
    override func configUI() {
        backgroundColor = .white
        addSubview(valueLabel)
        addSubview(separatorLine)
        addSubview(titleLabel)
        addSubview(starLabel)
        addSubview(arrowImageView)
    }
    
    override func configLayout() {
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(12)
            make.bottom.equalTo(-12)
        }
        starLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel.snp.right).offset(4)
            make.centerY.equalTo(titleLabel)
        }
        valueLabel.snp.makeConstraints { make in
            make.left.equalTo(separatorLine)
            make.right.equalTo(arrowImageView.snp.left).offset(-6)
            make.top.equalTo(0)
            make.bottom.equalTo(0)
            make.height.equalTo(44)
        }
        
        separatorLine.snp.makeConstraints { make in
            make.left.equalTo(90)
            make.right.equalTo(-12)
            make.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
        arrowImageView.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalToSuperview()
        }
    }
    
    // MARK: - 配置方法
    
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
        
        valueLabel.text = config.placeholder.isEmpty ? "添加标题" : config.placeholder
        titleLabel.text = config.title
        
        if let text = config.data as? String {
            valueLabel.text = text
            valueLabel.textColor = color_2B2C2F
            data = text
        }else{
            valueLabel.text = config.placeholder
            valueLabel.textColor = color_2B2C2F40
        }
    }
}
