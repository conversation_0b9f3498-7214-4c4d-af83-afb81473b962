//
//  SelectorItemView.swift
//  OXYPLay
//
//  Created by Renhw on 2023/7/5.
//

import UIKit
import SnapKit
import Then

class SelectorItemView: ListSelectItemView {

    // MARK: - UI组件

    lazy var iconImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFit
    }

    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }

    lazy var valueLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.8)
        $0.textAlignment = .right
    }
    lazy var placeholderLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.4)
    }
    lazy var requiredLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 10,weight: .regular)
        $0.textColor = UIColor(hexString: "#FF0000", transparency: 0.48)
        $0.backgroundColor = UIColor(hexString: "#FF0000", transparency: 0.08)
        $0.textAlignment = .center
        $0.setPadding(horizontal: 3, vertical: 2)
        $0.text = "必选"
        $0.cornerRadius = 4
        $0.setContentHuggingPriority(.required, for: .horizontal) // 水平方向紧贴内容
    }
    lazy var arrowImageView = UIImageView().then {
        $0.image = UIImage(named: "baselist_arrow")
    }

    // MARK: - 初始化方法

    override func configUI() {
        backgroundColor = .white
        addSubview(iconImageView)
        addSubview(titleLabel)
        addSubview(valueLabel)
        addSubview(arrowImageView)
        addSubview(requiredLabel)
        addSubview(placeholderLabel)

    }

    override func configLayout() {
        self.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
        iconImageView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(10)
            make.centerY.equalToSuperview()
        }
        placeholderLabel.snp.makeConstraints { make in
            make.right.equalTo(arrowImageView.snp.left).offset(-10)
            make.centerY.equalToSuperview()
            make.left.greaterThanOrEqualTo(requiredLabel.snp.right).offset(10)
        }
        arrowImageView.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalToSuperview()
            make.width.equalTo(14)
            make.height.equalTo(14)
        }

        valueLabel.snp.makeConstraints { make in
            make.right.equalTo(arrowImageView.snp.left).offset(-10)
            make.centerY.equalToSuperview()
            make.left.greaterThanOrEqualTo(requiredLabel.snp.right).offset(10)
        }
        requiredLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(titleLabel.snp.right).offset(8)
        }

    }
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
        if config.iconString.isEmpty{
            titleLabel.snp.remakeConstraints { make in
                make.left.equalTo(12)
                make.centerY.equalToSuperview()
            }
        }else{
            iconImageView.image = UIImage(named: config.iconString)
        }
        titleLabel.text = config.title
        placeholderLabel.text = config.placeholder
        valueLabel.text = config.subTitle
        requiredLabel.isHidden = !config.isRequired
        placeholderLabel.isHidden = !config.subTitle.isEmpty
    }
}
