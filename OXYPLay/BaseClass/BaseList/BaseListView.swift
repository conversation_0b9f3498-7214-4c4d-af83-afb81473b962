//
//  BaseListView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//

import UIKit
import Combine
import CombineCocoa

/// 列表项类型
enum ListItemType: Hashable {
    case titleInput
    case input
    case content
    case image
    case select
    case starselect
    case singleselect
    case orderConfirmation
    case orderLocation
    case InputIcon
    case InputPrice
    case retreatReason // 退款原因选择项
    case custom(String) // 自定义类型
    
    // 为关联值类型实现 Hashable
    func hash(into hasher: inout Hasher) {
        switch self {
        case .titleInput:
            hasher.combine(0)
        case .input:
            hasher.combine(1)
        case .content:
            hasher.combine(2)
        case .image:
            hasher.combine(3)
        case .select:
            hasher.combine(4)
        case .singleselect:
            hasher.combine(5)
        case .orderConfirmation:
            hasher.combine(6)
        case .orderLocation:
            hasher.combine(7)
        case .starselect:
            hasher.combine(8)
        case .InputIcon:
            hasher.combine(9)
        case .InputPrice:
            hasher.combine(10)
        case .retreatReason:
            hasher.combine(11)
        case .custom(let value):
            hasher.combine(10086)
            hasher.combine(value)
        }
    }
    
    // 为关联值类型实现 Equatable
    static func == (lhs: ListItemType, rhs: ListItemType) -> Bool {
        switch (lhs, rhs) {
        case (.titleInput, .titleInput),
            (.input, .input),
             (.content, .content),
             (.image, .image),
            (.select, .select),
            (.singleselect,.singleselect),
            (.orderConfirmation, .orderConfirmation),
            (.starselect, .starselect),
            (.orderLocation, .orderLocation),
            (.InputIcon, .InputIcon),
            (.InputPrice, .InputPrice),
            (.retreatReason, .retreatReason):

            return true
        case (.custom(let lhsValue), .custom(let rhsValue)):
            return lhsValue == rhsValue
        default:
            return false
        }
    }
}

/// 列表项配置
struct ListItemConfig {
    let type: ListItemType
    let identifier: String
    var data: Any?
    var isRequired: Bool = false
    var isSelect: Bool = false
    var placeholder: String = ""
    var iconString: String = ""
    var title: String = ""
    var subTitle: String = ""
    var maxLength: Int? = nil  // 字数限制

    // 便捷初始化方法
    init(type: ListItemType, identifier: String, data: Any? = nil, isRequired: Bool = false, isSelect: Bool = false, placeholder: String = "", iconString: String = "", title: String = "", subTitle: String = "", maxLength:Int = 100) {
        self.type = type
        self.identifier = identifier
        self.data = data
        self.isRequired = isRequired
        self.isSelect = isSelect
        self.placeholder = placeholder
        self.iconString = iconString
        self.title = title
        self.subTitle = subTitle
        self.maxLength = maxLength
    }

    // 专门为单选项提供的便捷初始化方法
    static func singleSelect(identifier: String, title: String, iconString: String = "", isSelected: Bool = false, subTitle: String = "",data: Any? = nil) -> ListItemConfig {
        return ListItemConfig(type: .singleselect, identifier: identifier,data: data, isSelect: isSelected, iconString: iconString, title: title, subTitle: subTitle)
    }
  
    static func select(identifier: String, title: String, iconString: String = "", isSelected: Bool = false, subTitle: String = "",data: Any? = nil) -> ListItemConfig {
        return ListItemConfig(type: .select, identifier: identifier,data: data, isSelect: isSelected, iconString: iconString, title: title, subTitle: subTitle)
    }
}

/// 列表项视图基类
class ListItemView: BaseView {
    // 列表项类型
    var type: ListItemType = .custom("default")
    var itemConfig:ListItemConfig?

    // 列表项标识符
    var identifier: String = ""
    
    // 列表项数据
    var data: Any?
    
    // 是否必填
    var isRequired: Bool = false
    //placeholder
    var placeholder: String = ""
    
    // 配置方法
    func configure(with config: ListItemConfig) {
        self.itemConfig = config
        self.type = config.type
        self.identifier = config.identifier
        self.data = config.data
        self.isRequired = config.isRequired
        self.placeholder = config.placeholder
    }
    
    // 获取数据
    func getData() -> Any? {
        return data
    }
    
    // 验证数据
    func validate() -> Bool {
        if isRequired {
            if let data = data as? String {
                return !data.isEmpty
            }
            return data != nil
        }
        return true
    }
}
/// 列表代理
protocol ListSelectItemViewDelegate: AnyObject {
    // 列表项点击事件
    func listItemViewClick(_ itemView: ListItemView,config:ListItemConfig)
    
}
/// 列表项视图基类
class ListSelectItemView:ListItemView {
    weak var delegate: ListSelectItemViewDelegate?{
        didSet{
            // 添加点击事件
            let tapGesture = UITapGestureRecognizer()
            tapGesture.tapPublisher
                .sink { [weak self] _ in
                    guard let self = self,let config = self.itemConfig else { return }
                    self.delegate?.listItemViewClick(self, config: config)
                }
                .store(in: &cancellables)
            
            self.addGestureRecognizer(tapGesture)
        }
    }

    override func configUI() {
       
    }
}
/// 列表代理
protocol BaseListViewDelegate: AnyObject {
    // 列表项数据变更
    func listViewUpdate(_ listView: BaseListView, with data: Any?)

    // 列表项点击事件
    func listViewClick(_ listView: BaseListView,config:ListItemConfig)

    // 列表验证失败
    func listViewValidate(_ listView: BaseListView, message: String)

    // @用户按钮点击事件
    func listViewMentionUser(_ listView: BaseListView, config: ListItemConfig)
}
extension BaseListViewDelegate{
    func listViewUpdate(_ listView: BaseListView, with data: Any?){}
    func listViewClick(_ listView: BaseListView,config:ListItemConfig){}
    func listViewValidate(_ listView: BaseListView, message: String){}
    func listViewMentionUser(_ listView: BaseListView, config: ListItemConfig){}
}
/// 通用列表视图
class BaseListView: BaseView {
    
    // MARK: - 属性
    
    weak var delegate: BaseListViewDelegate?
    
    // 列表项配置
    private var itemConfigs: [[ListItemConfig]] = [[]]

    // 分组标题
    private var sectionTitles: [String?] = []

    // 列表项视图
    private var itemViews: [ListItemView] = []
        
    // MARK: - UI组件
    
    lazy var scrollView = UIScrollView().then {
        $0.showsVerticalScrollIndicator = false
        $0.alwaysBounceVertical = true
        $0.keyboardDismissMode = .interactive
        $0.bounces = false
    }
    
    lazy var stackView = UIStackView().then {
        $0.axis = .vertical
        $0.alignment = .fill
        $0.distribution = .fill
        $0.spacing = 12
    }
    
    // MARK: - 初始化方法
    
    override func configUI() {
        backgroundColor = color_F6F8F9
        
        addSubview(scrollView)
        scrollView.addSubview(stackView)
    }
    
    override func configLayout() {
        scrollView.snp.makeConstraints{$0.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left: 12, bottom: 0, right: 12))}
        stackView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.bottom.lessThanOrEqualToSuperview()
            make.width.equalTo(scrollView)
        }
    }
    /// 设置列表项配置
    func setItems(_ configs: [[ListItemConfig]]) {
        self.itemConfigs = configs
        self.sectionTitles = []
        reloadData()
    }

    /// 设置带分组标题的列表项配置
    /// - Parameters:
    ///   - configs: 二维数组，每个子数组代表一个分组
    ///   - sectionTitles: 分组标题数组，与configs数组对应，nil表示无标题
    func setItems(_ configs: [[ListItemConfig]], sectionTitles: [String?]) {
        self.itemConfigs = configs
        self.sectionTitles = sectionTitles
        reloadData()
    }
    
    /// 重新加载数据
    func reloadData() {
        // 清除现有视图
        itemViews.removeAll()
        stackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // 创建新的列表项视图
        for (sectionIndex, configs) in itemConfigs.enumerated() {
            // 添加分组标题（如果有）
            if sectionIndex < sectionTitles.count, let sectionTitle = sectionTitles[sectionIndex] {
                let titleLabel = UILabel().then {
                    $0.text = sectionTitle
                    $0.textColor = UIColor(hexString: "#2B2C2F", transparency: 1)
                    $0.font = .systemFont(ofSize: 14, weight: .regular)
                }

                let titleContainer = UIView().then {
                    $0.backgroundColor = .clear
                }

                titleContainer.addSubview(titleLabel)
                titleLabel.snp.makeConstraints { make in
                    make.left.equalTo(12)
                    make.centerY.equalToSuperview()
                    make.top.equalTo(8)
                    make.bottom.equalTo(-8)
                }

                stackView.addArrangedSubview(titleContainer)
            }

            let substackView = UIStackView().then {
                $0.axis = .vertical
                $0.alignment = .fill
                $0.distribution = .fill
                $0.spacing = 0
                $0.backgroundColor = .white
                $0.layer.cornerRadius = 16
                $0.masksToBounds = true
            }

            for (subIndex, config) in configs.enumerated() {
                let itemView = getItemView(config: config)
                itemView.configure(with: config)
                itemView.tag = subIndex
                itemViews.append(itemView)
                substackView.addArrangedSubview(itemView)
            }

            stackView.addArrangedSubview(substackView)
        }
    }
    func getItemView(config:ListItemConfig)->ListItemView{
        switch config.type {
        case .content:
            let itemView = ContentInputItemView()
            // 绑定@用户按钮点击事件
            itemView.mentionUserPublisher
                .sink { [weak self] _ in
                    guard let self = self else { return }
                    self.delegate?.listViewMentionUser(self, config: config)
                }
                .store(in: &itemView.cancellables)
            return itemView
        case .titleInput:
            return TitleInputItemView()
        case .input:
            return InputItemView()
        case .image:
            return ImagePickerItemView()
        case .singleselect:
            let itemView = SingleSelectorItemView()
            itemView.delegate = self
            return itemView
        case .orderLocation:
            let itemView = OrderLocationItemView()
            itemView.delegate = self
            return itemView
        case .orderConfirmation:
            let itemView = OrderConfirmation​ItemView()
            itemView.delegate = self
            return itemView
        case .starselect:
            let itemView = StarSelectItemView()
            itemView.delegate = self
            return itemView
        case .InputIcon:
            let itemView = InputIconItemView()
            // 有值显示price无则隐藏
            itemView.titleTextField.textPublisher
                .sink { [weak self] text in
                    guard let priceView = self?.itemViews.first(where: {$0.isMember(of: InputPriceItemView.self)}) else { return }
                    guard let text = text else  {
                        priceView.isHidden = true
                        return
                    }
                    priceView.isHidden = text.isEmpty
                }
                .store(in: &cancellables)
            return itemView
        case .InputPrice:
            let itemView = InputPriceItemView()
            return itemView
        case .retreatReason:
            let itemView = RetreatReasonItemView()
            itemView.delegate = self
            return itemView
        default:
            let itemView = SelectorItemView()
            itemView.delegate = self
            return itemView
        }
    }
    func updateItem(with config: ListItemConfig) {
        for (index,item) in self.itemViews.enumerated(){
            if item.identifier == config.identifier{
                self.itemViews[index].configure(with: config)
            }
        }
    }
    func getItem(identifier:String)->ListItemView{
        guard let item = self.itemViews.first(where: {$0.identifier == identifier}) else {
            return ListItemView()
        }
        return item
    }
    
    /// 获取所有列表项数据
    func getAllData() -> [String: Any] {
        var data: [String: Any] = [:]
        for itemView in itemViews {
            if let itemData = itemView.getData() {
                if let dic = itemData as? [String:Any] {
                    data = data.merging(dic) { _, new in new }
                }else{
                    data[itemView.identifier] = itemData
                }
            }
        }
        return data
    }

    /// 获取选中的单选项标识符
    func getSelectedSingleSelectIdentifier() -> String? {
        // 检查普通单选项
        let singleSelectItems = itemViews.filter { $0.type == .singleselect }
        for item in singleSelectItems {
            if let singleSelectItem = item as? SingleSelectorItemView,
               singleSelectItem.isSelected() {
                return singleSelectItem.identifier
            }
        }

        // 检查退款原因选择项
        let retreatReasonItems = itemViews.filter { $0.type == .retreatReason }
        for item in retreatReasonItems {
            if let retreatReasonItem = item as? RetreatReasonItemView,
               retreatReasonItem.isSelected() {
                return retreatReasonItem.identifier
            }
        }

        return nil
    }

    /// 设置指定标识符的单选项为选中状态
    func setSingleSelectItem(identifier: String, selected: Bool = true) {
        // 如果要选中某项，先清除所有选中状态
        if selected {
            // 清除普通单选项
            let singleSelectItems = itemViews.filter { $0.type == .singleselect }
            for item in singleSelectItems {
                if let singleSelectItem = item as? SingleSelectorItemView {
                    singleSelectItem.setSelected(false)
                }
            }

            // 清除退款原因选择项
            let retreatReasonItems = itemViews.filter { $0.type == .retreatReason }
            for item in retreatReasonItems {
                if let retreatReasonItem = item as? RetreatReasonItemView {
                    retreatReasonItem.setSelected(false)
                }
            }
        }

        // 设置指定项的选中状态
        if let targetItem = itemViews.first(where: { $0.identifier == identifier && $0.type == .singleselect }) as? SingleSelectorItemView {
            targetItem.setSelected(selected)
        } else if let targetItem = itemViews.first(where: { $0.identifier == identifier && $0.type == .retreatReason }) as? RetreatReasonItemView {
            targetItem.setSelected(selected)
        }
    }
} 
extension BaseListView:ListSelectItemViewDelegate{
    func listItemViewClick(_ itemView: ListItemView, config: ListItemConfig) {
        // 处理单选逻辑
        if config.type == .singleselect || config.type == .retreatReason {
            handleSingleSelection(selectedItem: itemView, config: config)
        }

        self.delegate?.listViewClick(self, config: config)
    }

    /// 处理单选逻辑
    private func handleSingleSelection(selectedItem: ListItemView, config: ListItemConfig) {
        if config.type == .singleselect {
            // 找到同一组中的所有singleselect项
            let singleSelectItems = itemViews.filter { $0.type == .singleselect }

            // 清除所有单选项的选中状态
            for item in singleSelectItems {
                if let singleSelectItem = item as? SingleSelectorItemView {
                    singleSelectItem.setSelected(false)
                }
            }

            // 设置当前点击项为选中状态
            if let selectedSingleSelectItem = selectedItem as? SingleSelectorItemView {
                selectedSingleSelectItem.setSelected(true)
            }
        } else if config.type == .retreatReason {
            // 找到同一组中的所有retreatReason项
            let retreatReasonItems = itemViews.filter { $0.type == .retreatReason }

            // 清除所有退款原因选择项的选中状态
            for item in retreatReasonItems {
                if let retreatReasonItem = item as? RetreatReasonItemView {
                    retreatReasonItem.setSelected(false)
                }
            }

            // 设置当前点击项为选中状态
            if let selectedRetreatReasonItem = selectedItem as? RetreatReasonItemView {
                selectedRetreatReasonItem.setSelected(true)
            }
        }

        // 通知数据更新
        self.delegate?.listViewUpdate(self, with: config.identifier)
    }
}
