//
//  RetreatReasonItemView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

import UIKit
import Combine
import CombineCocoa

/// 退款原因选择项视图
class RetreatReasonItemView: ListSelectItemView {
    
    // MARK: - Properties
    
    /// 是否显示输入框
    private var isInputVisible: Bool = false {
        didSet {
            updateInputVisibility()
        }
    }
    
    // MARK: - UI组件
    
    /// 选择按钮
    lazy var selectButton = UIButton().then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
    }
    
    /// 标题标签
    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        $0.textColor = color_2B2C2F
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }
    
    /// 输入框容器
    lazy var inputContainerView = UIView().then {
        $0.backgroundColor = UIColor(hexString: "F6F8F9")
        $0.layer.cornerRadius = 8
        $0.masksToBounds = true
        $0.isHidden = true
    }
    
    /// 输入框
    lazy var inputTextView = PlaceholderTextView().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        $0.textColor = color_2B2C2F
        $0.backgroundColor = .clear
        $0.isScrollEnabled = false
        $0.textContainerInset = UIEdgeInsets(top: 8, left: 8, bottom: 8, right: 8)
        $0.textContainer.lineFragmentPadding = 0
        $0.placeholder = "请输入具体退款原因"
        $0.placeholderColor = color_2B2C2F40
        $0.delegate = self
    }
    
    /// 分割线
    lazy var separatorLine = UIView().then {
        $0.backgroundColor = .white
    }
    
    // MARK: - 初始化方法
    
    override func configUI() {
        backgroundColor = .white

        addSubview(selectButton)
        addSubview(titleLabel)
        addSubview(inputContainerView)
        addSubview(separatorLine)

        inputContainerView.addSubview(inputTextView)
    }
    
    override func configLayout() {
        // 选择按钮约束
        selectButton.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalTo(titleLabel)
            make.width.height.equalTo(14)
        }
        
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(12)
            make.right.equalTo(-12)
        }
        
        // 输入框容器约束
        inputContainerView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.height.equalTo(60)
        }
        
        // 输入框约束
        inputTextView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 分割线约束
        separatorLine.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalToSuperview()
            make.height.equalTo(1)
        }

        // 设置初始高度约束
        self.snp.makeConstraints { make in
            make.height.equalTo(44) // 初始高度
        }

        // 设置事件绑定
        setupBindings()
    }
    
    // MARK: - 事件绑定
    
    override func setupBindings() {
        // 输入框文本变化监听
        inputTextView.textPublisher
            .sink { [weak self] text in
                self?.updateData()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 配置方法
    
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
        
        titleLabel.text = config.title
        selectButton.isSelected = config.isSelect
        
        // 如果是选中状态且标题包含"其他"，显示输入框
        if config.isSelect && config.title.contains("其他") {
            isInputVisible = true
        } else {
            isInputVisible = false
        }
        
        // 设置输入框内容
        if let inputText = config.data as? String {
            inputTextView.text = inputText
        }
        
        updateData()
    }
    
    // MARK: - 公共方法
    
    /// 设置选中状态
    func setSelected(_ selected: Bool) {
        selectButton.isSelected = selected
        isInputVisible = selected
        
        // 更新配置
        itemConfig?.isSelect = selected
        updateData()
    }
    
    /// 获取选中状态
    func isSelected() -> Bool {
        return selectButton.isSelected
    }
    
    /// 获取输入内容
    func getInputText() -> String? {
        return inputTextView.text.isEmpty ? nil : inputTextView.text
    }
    
    // MARK: - 私有方法
    
    /// 更新输入框显示状态
    private func updateInputVisibility() {
        inputContainerView.isHidden = !isInputVisible
        updateHeightConstraint()
    }
    
    /// 更新高度约束
    private func updateHeightConstraint() {
        let baseHeight: CGFloat = 44
        let inputHeight: CGFloat = isInputVisible ? 68 : 0 
        let totalHeight = baseHeight + inputHeight

        // 移除之前的高度约束，重新设置
        self.snp.updateConstraints { make in
            make.height.equalTo(totalHeight)
        }
    }
    
    /// 更新数据
    private func updateData() {
        if isSelected() {
            if titleLabel.text?.contains("其他") == true {
                // 其他选项返回输入的文本
                data = inputTextView.text.isEmpty ? titleLabel.text : inputTextView.text
            } else {
                // 普通选项返回标题
                data = titleLabel.text
            }
        } else {
            data = nil
        }
    }
    
    // MARK: - Override Methods
    
    override func getData() -> Any? {
        return data
    }
}

// MARK: - UITextViewDelegate

extension RetreatReasonItemView: UITextViewDelegate {
    
    func textViewDidChange(_ textView: UITextView) {
        updateData()
    }
    
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        // 限制字数为100字
        let currentText = textView.text ?? ""
        guard let stringRange = Range(range, in: currentText) else { return false }
        let newText = currentText.replacingCharacters(in: stringRange, with: text)
        return newText.count <= 100
    }
}
