//
//  CustomDatePicker.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/1.
//

import UIKit
import SnapKit
import Then
import Combine
import SwiftDate

/// 自定义日期选择器配置
public struct CustomDatePickerConfig {
    /// 年份范围类型
    public enum YearRangeType {
        case recent(months: Int)  // 最近几个月
        case past(years: Int)     // 过去几年
        case custom(startYear: Int, endYear: Int)  // 自定义范围
    }
    
    /// 年份范围
    public let yearRange: YearRangeType
    
    /// 是否显示年份后缀
    public let showYearSuffix: Bool
    
    /// 是否显示月份后缀
    public let showMonthSuffix: Bool
    
    /// 是否需要日期验证
    public let needValidation: Bool
    
    public init(
        yearRange: YearRangeType = .recent(months: 24),
        showYearSuffix: Bool = true,
        showMonthSuffix: Bool = true,
        needValidation: Bool = false
    ) {
        self.yearRange = yearRange
        self.showYearSuffix = showYearSuffix
        self.showMonthSuffix = showMonthSuffix
        self.needValidation = needValidation
    }
}

/// 自定义日期选择器
class CustomDatePicker: BaseView {
    
    // MARK: - Properties
    
    /// 配置
    private let config: CustomDatePickerConfig
    
    /// 日期选择完成回调
    let dateSelectedPublisher = PassthroughSubject<(year: Int, month: Int), Never>()
    
    /// 可选择的年份列表
    private var availableYears: [Int] = []
    
    /// 可选择的月份列表
    private var availableMonths: [Int] = Array(1...12)
    
    /// 当前选中的年份
    private var selectedYear: Int = 0
    
    /// 当前选中的月份
    private var selectedMonth: Int = 0
    
    // MARK: - UI Components
    
    /// 年月选择器
    private lazy var yearMonthPicker = UIPickerView().then {
        $0.delegate = self
        $0.dataSource = self
    }
    
    // MARK: - 初始化

    init(config: CustomDatePickerConfig = CustomDatePickerConfig()) {
        self.config = config
        super.init(frame: .zero)
        setupAvailableYears()
        setupDefaultSelection()
    }

    override init(frame: CGRect) {
        self.config = CustomDatePickerConfig()
        super.init(frame: frame)
        setupAvailableYears()
        setupDefaultSelection()
    }

    required init?(coder: NSCoder) {
        self.config = CustomDatePickerConfig()
        super.init(coder: coder)
        setupAvailableYears()
        setupDefaultSelection()
    }
    
    // MARK: - UI Setup
    
    override func configUI() {
        backgroundColor = .clear
        addSubview(yearMonthPicker)
    }
    
    override func configLayout() {
        yearMonthPicker.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Private Methods
    
    /// 设置可选择的年份列表
    private func setupAvailableYears() {
        let now = Date()
        
        switch config.yearRange {
        case .recent(let months):
            let earliestDate = now - months.months
            let currentYear = now.year
            let earliestYear = earliestDate.year
            availableYears = Array(earliestYear...currentYear)
            
        case .past(let years):
            let currentYear = now.year
            let earliestYear = currentYear - years
            availableYears = Array(earliestYear...currentYear)
            
        case .custom(let startYear, let endYear):
            availableYears = Array(startYear...endYear)
        }
    }
    
    /// 设置默认选择
    private func setupDefaultSelection() {
        let now = Date()
        selectedYear = now.year
        selectedMonth = now.month
    }
    
    /// 设置初始选择
    private func setupInitialSelection() {
        // 设置选择器的初始位置
        if let yearIndex = availableYears.firstIndex(of: selectedYear) {
            yearMonthPicker.selectRow(yearIndex, inComponent: 0, animated: false)
        }
        
        if selectedMonth >= 1 && selectedMonth <= 12 {
            yearMonthPicker.selectRow(selectedMonth - 1, inComponent: 1, animated: false)
        }
    }
    
    /// 验证选择的日期是否在有效范围内
    private func validateSelection() {
        guard config.needValidation else { return }
        
        let now = Date()
        
        switch config.yearRange {
        case .recent(let months):
            let earliestDate = now - months.months
            
            // 构造选择的日期
            let selectedDateInRegion = DateInRegion(year: selectedYear, month: selectedMonth, day: 1)
            let selectedDate = selectedDateInRegion.date
            
            // 如果选择的日期超出范围，自动调整
            if selectedDate > now {
                // 如果选择的日期晚于当前日期，调整为当前月份
                selectedYear = now.year
                selectedMonth = now.month
                updatePickerSelection()
            } else if selectedDate < earliestDate {
                // 如果选择的日期早于最早日期，调整为最早月份
                selectedYear = earliestDate.year
                selectedMonth = earliestDate.month
                updatePickerSelection()
            }
        default:
            break
        }
    }
    
    /// 更新选择器的选择状态
    private func updatePickerSelection() {
        if let yearIndex = availableYears.firstIndex(of: selectedYear) {
            yearMonthPicker.selectRow(yearIndex, inComponent: 0, animated: true)
        }
        
        if selectedMonth >= 1 && selectedMonth <= 12 {
            yearMonthPicker.selectRow(selectedMonth - 1, inComponent: 1, animated: true)
        }
    }
    
    // MARK: - Public Methods
    
    /// 设置当前选中的日期
    /// - Parameters:
    ///   - year: 年份
    ///   - month: 月份
    func setSelectedDate(year: Int, month: Int) {
        selectedYear = year
        selectedMonth = month
        setupInitialSelection()
    }
    
    /// 设置当前选中的日期字符串
    /// - Parameter dateString: 日期字符串，格式为 "yyyy-MM"
    func setSelectedDate(dateString: String) {
        if let date = dateString.toDate("yyyy-MM")?.date {
            selectedYear = date.year
            selectedMonth = date.month
            setupInitialSelection()
        }
    }
    
    /// 获取当前选中的日期字符串
    /// - Returns: 格式为 "yyyy-MM" 的日期字符串
    func getSelectedDateString() -> String {
        return String(format: "%04d-%02d", selectedYear, selectedMonth)
    }
    
    /// 获取当前选中的年月
    /// - Returns: (年份, 月份)
    func getSelectedYearMonth() -> (year: Int, month: Int) {
        return (selectedYear, selectedMonth)
    }
    
    /// 刷新选择器数据
    func reloadData() {
        setupAvailableYears()
        yearMonthPicker.reloadAllComponents()
        setupInitialSelection()
    }
}

// MARK: - UIPickerViewDataSource & UIPickerViewDelegate

extension CustomDatePicker: UIPickerViewDataSource, UIPickerViewDelegate {
    
    func numberOfComponents(in pickerView: UIPickerView) -> Int {
        return 2 // 年份和月份两列
    }
    
    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        switch component {
        case 0: // 年份列
            return availableYears.count
        case 1: // 月份列
            return availableMonths.count
        default:
            return 0
        }
    }
    
    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        switch component {
        case 0: // 年份列
            let year = availableYears[row]
            return config.showYearSuffix ? "\(year)年" : "\(year)"
        case 1: // 月份列
            let month = availableMonths[row]
            return config.showMonthSuffix ? "\(month)月" : "\(month)"
        default:
            return nil
        }
    }
    
    func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
        switch component {
        case 0: // 年份列
            selectedYear = availableYears[row]
        case 1: // 月份列
            selectedMonth = availableMonths[row]
        default:
            break
        }
        
        // 验证选择的日期
        validateSelection()
        
        // 发送选择事件
        dateSelectedPublisher.send((selectedYear, selectedMonth))
    }
}
