import UIKit

class BasePresentController: BaseViewController {
    
    var presentationHeight: CGFloat? {
        return nil
    }
    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    
    private lazy var closeButton = UIButton().then {
        $0.setImage(UIImage(named: "closebuttonicon"), for: .normal)
    }
    lazy var contentView = UIView()
    lazy var topView = UIView()
    lazy var backView = UIView().then {
        $0.backgroundColor = color_F6F8F9
        $0.layer.cornerRadius = 16
        $0.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        $0.masksToBounds = true
    }
    lazy var bottomView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        $0.masksToBounds = true
    }
    lazy var bottomButton = BaseGradientButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = color_blue
        $0.isRounded = true
    }
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
    }
   
    // MARK: - UI设置
    
    func setupUI() {
        view.backgroundColor = .clear
        view.addSubview(topView)
        view.addSubview(backView)
        backView.addSubview(closeButton)
        backView.addSubview(titleLabel)
        backView.addSubview(contentView)
        backView.addSubview(bottomView)
        bottomView.addSubview(bottomButton)
        topView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(backView.snp.top)
        }
        backView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            if let presentationHeight = presentationHeight{
                make.height.equalTo(presentationHeight)
            }
        }
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(14)
            make.right.equalTo(-14)
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(30)
            make.centerX.equalToSuperview()
            make.height.equalTo(20)
        }
        contentView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(24)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomView.snp.top).offset(-12)
        }
        bottomView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
        bottomButton.snp.makeConstraints { make in
            make.left.top.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
        }
    }
    override func setupBindings() {
        // 添加点击事件
        let tapGesture = UITapGestureRecognizer()
        tapGesture.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)
        
        topView.addGestureRecognizer(tapGesture)
        closeButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)
    }
    func hideBottomBar(){
        bottomView.isHidden = true
        contentView.snp.updateConstraints { make in
            make.bottom.equalTo(bottomView.snp.top).offset(ScreenInfo.totalTabBarHeight)
        }
    }
    func configView(title:String?, bottomTitle:String = "确定"){
        self.bottomButton.setTitle(bottomTitle, for: .normal)
        self.titleLabel.text = title
    }
    
}
