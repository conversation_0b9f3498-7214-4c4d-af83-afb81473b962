//
//  PlaceholderTextView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

class PlaceholderTextView: UITextView {
    // MARK: - 属性
    var placeholder: String = "" {
        didSet { setNeedsDisplay() }
    }
    
    var placeholderColor: UIColor = color_2B2C2F40 {
        didSet { setNeedsDisplay() }
    }
    
    var placeholderFont: UIFont = UIFont.systemFont(ofSize: 13,weight: .regular) {
        didSet { setNeedsDisplay() }
    }
  
    // MARK: - 初始化
    override init(frame: CGRect, textContainer: NSTextContainer?) {
        super.init(frame: frame, textContainer: textContainer)
        setup()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setup()
    }
    
    private func setup() {
        // 监听文本变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(textDidChange),
            name: UITextView.textDidChangeNotification,
            object: self
        )
        backgroundColor = .clear
    }
    
    // MARK: - 文本变化处理
    @objc private func textDidChange() {
        setNeedsDisplay() // 触发重绘
    }
    
    // MARK: - 绘制占位符
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        // 无文本时绘制占位符
        guard text.isEmpty else { return }
        
        let attributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: placeholderColor,
            .font: placeholderFont
        ]
        
        // 计算绘制区域（留出边距）
        let drawRect = rect.inset(by: textContainerInset)
        placeholder.draw(in: drawRect, withAttributes: attributes)
    }
    
    // MARK: - 清理通知
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
