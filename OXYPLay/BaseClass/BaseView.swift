//
//  BaseView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/2.
//

import UIKit
import Combine

public class BaseView: UIView {
    /// 存储订阅
    var cancellables = Set<AnyCancellable>()
    
    // MARK: - 渐变色属性
    private var gradientLayer: CAGradientLayer?
    private var gradientColors: [UIColor]?
    private var gradientDirection: GradientDirection = .vertical
    
    // 渐变方向枚举
    public enum GradientDirection {
        case vertical   // 垂直从上到下
        case horizontal // 水平从左到右
        
        var startPoint: CGPoint {
            switch self {
            case .vertical: return CGPoint(x: 0.5, y: 0)
            case .horizontal: return CGPoint(x: 0, y: 0.5)
            }
        }
        
        var endPoint: CGPoint {
            switch self {
            case .vertical: return CGPoint(x: 0.5, y: 1)
            case .horizontal: return CGPoint(x: 1, y: 0.5)
            }
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        configUI()
        configLayout()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        configUI()
        configLayout()
    }
    
    /// UI配置
    public func configUI() {}
    
    /// UI布局
    public func configLayout() {}
    
    func setupBindings() {}
    
    // MARK: - 渐变色方法
    /// 添加渐变色背景
    /// - Parameters:
    ///   - colors: 渐变颜色数组（至少需要2个颜色）
    ///   - direction: 渐变方向（垂直或水平）
    public func addGradientBackground(
        colors: [UIColor],
        direction: GradientDirection = .vertical
    ) {
        // 移除现有渐变层
        removeGradientBackground()
        
        // 创建新渐变层
        let gradient = CAGradientLayer()
        gradient.colors = colors.map { $0.cgColor }
        gradient.startPoint = direction.startPoint
        gradient.endPoint = direction.endPoint
        gradient.frame = self.bounds
        gradient.cornerRadius = self.layer.cornerRadius
        layer.insertSublayer(gradient, at: 0)
        
        // 保存引用和配置
        gradientLayer = gradient
        gradientColors = colors
        gradientDirection = direction
        
        // 监听尺寸变化
        setupSizeObserver()
    }
    
    /// 移除渐变色背景
    public func removeGradientBackground() {
        gradientLayer?.removeFromSuperlayer()
        gradientLayer = nil
        gradientColors = nil
    }
    
    /// 更新渐变色
    public func updateGradientColors(_ colors: [UIColor]) {
        guard let gradient = gradientLayer else { return }
        gradient.colors = colors.map { $0.cgColor }
        gradientColors = colors
    }
    
    /// 更新渐变方向
    public func updateGradientDirection(_ direction: GradientDirection) {
        guard let gradient = gradientLayer else { return }
        gradientDirection = direction
        gradient.startPoint = direction.startPoint
        gradient.endPoint = direction.endPoint
    }
    
    // MARK: - 布局处理
    private func setupSizeObserver() {
        // 监听尺寸变化
        self.publisher(for: \.bounds)
            .sink { [weak self] _ in
                self?.updateGradientFrame()
            }
            .store(in: &cancellables)
    }
    
    private func updateGradientFrame() {
        gradientLayer?.frame = self.bounds
        gradientLayer?.cornerRadius = self.layer.cornerRadius
    }
    
    public override func layoutSubviews() {
        super.layoutSubviews()
        updateGradientFrame()
    }
    
    // MARK: - 旋转适配
    public override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        super.traitCollectionDidChange(previousTraitCollection)
        
        // 适配深色模式
        if #available(iOS 13.0, *),
           traitCollection.hasDifferentColorAppearance(comparedTo: previousTraitCollection),
           let colors = gradientColors {
            updateGradientColors(colors)
        }
    }
}
