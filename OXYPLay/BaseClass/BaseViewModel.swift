//
//  BaseViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2023/6/10.
//

import Foundation
import Combine
import SmartCodable
import Moya

/// 网络请求状态
enum RequestState {
    /// 空闲状态
    case idle
    /// 加载中
    case loading
    /// 请求成功
    case success
    /// 请求失败
    case failure(String)
}

/// 刷新状态枚举 - 统一管理下拉刷新和上拉加载状态
enum RefreshState {
    /// 空闲状态
    case idle
    /// 下拉刷新中
    case headerRefreshing
    /// 上拉加载中
    case footerLoading
    /// 刷新成功
    case refreshSuccess
    /// 加载更多成功
    case loadMoreSuccess
    /// 刷新失败
    case refreshFailure(String)
    /// 加载更多失败
    case loadMoreFailure(String)
    /// 没有更多数据
    case noMoreData
}

/// 基础ViewModel类
/// 提供常用的属性和方法，作为所有ViewModel的基类
class BaseViewModel {
    // MARK: - 属性
    
    /// 存储所有的订阅
    var cancellables = Set<AnyCancellable>()
    
    /// 网络请求状态
    @Published var requestState: RequestState = .idle

    /// 刷新状态 - 统一管理下拉刷新和上拉加载
    @Published var refreshState: RefreshState = .idle

    /// 是否正在加载
    @Published var isLoading: Bool = false
    /// 是否正在刷新
    @Published  var isRefreshing: Bool = false
    /// 是否还有更多
    @Published  var hasMoreData: Bool = true

    /// 空数据状态
    @Published var emptyDataSetType: EmptyDataSetType? = nil

    /// 当前页码
    var currentPage = 1

    /// 每页数量
    let pageSize = 10

    /// 图片上传管理器
    lazy var imageUploadManager = ImageUploadManager()

    // MARK: - 初始化
    
    init() {
        setupBindings()
    }
    
    // MARK: - 绑定
    
    /// 设置数据绑定
    /// 子类可重写此方法添加自定义绑定
    func setupBindings() {
        // 监听网络请求状态，更新isLoading
        $requestState
            .sink { [weak self] state in
                switch state {
                case .idle:
                    self?.isLoading = false
                case .loading:
                    self?.isLoading = true
                case .success:
                    self?.isLoading = false
                case .failure:
                    self?.isLoading = false
                }
            }
            .store(in: &cancellables)

        // 监听刷新状态，自动更新isRefreshing
        $refreshState
            .sink { [weak self] state in
                switch state {
                case .idle:
                    self?.isRefreshing = false
                case .headerRefreshing:
                    self?.isRefreshing = true
                case .footerLoading:
                    self?.isRefreshing = false
                case .refreshSuccess, .loadMoreSuccess:
                    self?.isRefreshing = false
                case .refreshFailure, .loadMoreFailure:
                    self?.isRefreshing = false
                case .noMoreData:
                    self?.isRefreshing = false
                    // 注意：hasMoreData在handlePageResponse中已经设置，这里不需要重复设置
                }
            }
            .store(in: &cancellables)
    }
    // MARK: - 刷新相关方法

    /// 刷新数据 - 子类重写实现具体逻辑
    func refreshData() {
        // 子类重写实现具体的刷新逻辑
    }

    /// 加载更多数据 - 子类重写实现具体逻辑
    func loadMoreData() {
        // 子类重写实现具体的加载更多逻辑
    }

    /// 便捷的分页数据请求方法
    /// - Parameters:
    ///   - target: 网络请求目标
    ///   - type: 数据模型类型
    ///   - isRefresh: 是否为刷新操作
    ///   - completion: 数据处理回调
    /// - Returns: Publisher
    func requestPageData<T: SmartCodable>(
        _ target: TargetType,
        type: T.Type,
        isRefresh: Bool,
        completion: @escaping ([T], Bool) -> Void
    ) -> AnyPublisher<PageResponse<T>, NetworkError> {

        return requestPage(target, type: type)
            .handleEvents(
                receiveOutput: { [weak self] pageResponse in
                    // 处理分页响应
                    self?.handlePageResponse(pageResponse, isRefresh: isRefresh, completion: completion)
                },
                receiveCompletion: { [weak self] result in
                    if case .failure(let error) = result {
                        self?.handleNetworkError(error, isRefresh: isRefresh)
                    }
                }
            )
            .eraseToAnyPublisher()
    }

    /// 处理分页响应
    /// - Parameters:
    ///   - pageResponse: 分页响应数据
    ///   - isRefresh: 是否为刷新操作
    ///   - completion: 数据处理回调
    func handlePageResponse<T: SmartCodable>(
        _ pageResponse: PageResponse<T>,
        isRefresh: Bool,
        completion: @escaping ([T], Bool) -> Void
    ) {
        let newData = pageResponse.list
        let totalCount = pageResponse.total

        // 更新是否还有更多数据的逻辑
        // 优先使用总数据量进行判断，这样更准确
        if totalCount >= 0 {
            // 如果有总数据量信息，使用总数据量判断
            let currentDataCount = isRefresh ? newData.count : (currentPage - 1) * pageSize + newData.count
            hasMoreData = currentDataCount < totalCount

            // 调试信息
            print("📊 分页数据判断(使用总数): 刷新=\(isRefresh), 当前页=\(currentPage), 本次数据=\(newData.count), 累计数据=\(currentDataCount), 总数据量=\(totalCount), 还有更多=\(hasMoreData)")
        } else {
            // 如果没有总数据量信息，则根据返回的数据数量判断
            // 如果返回的数据数量小于请求的页面大小，说明没有更多数据了
            hasMoreData = newData.count >= pageSize

            // 调试信息
            print("📊 分页数据判断(使用数量): 刷新=\(isRefresh), 当前页=\(currentPage), 返回数据量=\(newData.count), 页面大小=\(pageSize), 还有更多=\(hasMoreData)")
        }

        // 更新刷新状态
        if isRefresh {
            refreshState = .refreshSuccess
        } else {
            if hasMoreData {
                refreshState = .loadMoreSuccess
            } else {
                refreshState = .noMoreData
            }
        }

        // 回调处理数据
        completion(newData, isRefresh)
    }

    /// 处理网络错误
    /// - Parameters:
    ///   - error: 网络错误
    ///   - isRefresh: 是否为刷新操作
    func handleNetworkError(_ error: NetworkError, isRefresh: Bool) {
        // 恢复页码
        if !isRefresh {
            currentPage = max(1, currentPage - 1)
        }

        // 更新刷新状态
        let errorMessage = error.localizedDescription
        if isRefresh {
            refreshState = .refreshFailure(errorMessage)
        } else {
            refreshState = .loadMoreFailure(errorMessage)
        }
    }
    // MARK: - 网络请求
    
    /// 发送普通请求
    /// - Parameter target: 接口
    /// - Returns: ResponseModel的Publisher
    func request(_ target: TargetType) -> AnyPublisher<ResponseModel, NetworkError> {
        requestState = .loading
        
        return CombineNetworkManager.shared.request(target)
            .handleEvents(
                receiveOutput: { [weak self] _ in
                    self?.requestState = .success
                },
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.requestState = .failure(error.localizedDescription)
                    }
                }
            )
            .eraseToAnyPublisher()
    }
    
    /// 请求单个模型
    /// - Parameters:
    ///   - target: 接口
    ///   - type: 模型类型
    /// - Returns: 模型的Publisher
    func requestModel<T: SmartCodable>(_ target: TargetType, type: T.Type) -> AnyPublisher<T, NetworkError> {
        requestState = .loading
        
        return CombineNetworkManager.shared.requestModel(target, type: type)
            .handleEvents(
                receiveOutput: { [weak self] _ in
                    self?.requestState = .success
                },
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.requestState = .failure(error.localizedDescription)
                    }
                }
            )
            .eraseToAnyPublisher()
    }
    
    /// 请求模型数组
    /// - Parameters:
    ///   - target: 接口
    ///   - type: 模型数组类型
    /// - Returns: 模型数组的Publisher
    func requestModels<T: SmartCodable>(_ target: TargetType, type: [T].Type) -> AnyPublisher<[T], NetworkError> {
        requestState = .loading
        
        return CombineNetworkManager.shared.requestModels(target, type: type)
            .handleEvents(
                receiveOutput: { [weak self] _ in
                    self?.requestState = .success
                },
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.requestState = .failure(error.localizedDescription)
                    }
                }
            )
            .eraseToAnyPublisher()
    }
    
    /// 请求分页数据
    /// - Parameters:
    ///   - target: 接口
    ///   - type: 模型类型
    /// - Returns: 分页响应的Publisher
    func requestPage<T: SmartCodable>(_ target: TargetType, type: T.Type) -> AnyPublisher<PageResponse<T>, NetworkError> {
        requestState = .loading
        
        return CombineNetworkManager.shared.requestModel(target, type: PageResponse<T>.self)
            .handleEvents(
                receiveOutput: { [weak self] _ in
                    self?.requestState = .success
                },
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.requestState = .failure(error.localizedDescription)
                    }
                }
            )
            .eraseToAnyPublisher()
    }
    
    // MARK: - 辅助方法
    
    /// 开始加载
    func startLoading() {
        requestState = .loading
    }
    
    /// 结束加载（成功）
    func endLoadingSuccess() {
        requestState = .success
    }
    
    /// 结束加载（失败）
    /// - Parameter message: 错误信息
    func endLoadingFailure(_ message: String) {
        requestState = .failure(message)
    }
    
    /// 重置状态
    func resetState() {
        requestState = .idle
        refreshState = .idle
    }

    /// 重置刷新状态
    func resetRefreshState() {
        refreshState = .idle
        currentPage = 1
        hasMoreData = true
    }
    
    /// 取消所有请求
    func cancelAllRequests() {
        cancellables.removeAll()
        requestState = .idle
    }
    
    // MARK: - 错误处理
    
    /// 处理错误
    /// - Parameter error: 错误对象
    /// - Returns: 错误信息
    func handleError(_ error: Error) -> String {
        return error.localizedDescription
    }

    /// 处理网络错误（重载方法）
    /// - Parameters:
    ///   - error: 网络错误
    ///   - operation: 操作描述
    func handleError(_ error: NetworkError, operation: String = "请求") {
        print("[\(operation)]失败: \(error.localizedDescription)")

        switch error {
        case .networkError(let response):
            print("服务器错误: \(response.message)")
            emptyDataSetType = .error
        case .decodingError(let message):
            print("数据解析错误: \(message)")
            emptyDataSetType = .error
        case .noConnection:
            print("网络连接失败")
            emptyDataSetType = .noNetwork
        case .tokenExpired:
            print("Token已过期，需要重新登录")
            emptyDataSetType = .error
        case .tokenError:
            print("Token错误")
            emptyDataSetType = .error
        }
    }

    // MARK: - 空数据状态管理

    /// 设置空数据状态
    /// - Parameter type: 空数据状态类型
    func setEmptyDataSet(type: EmptyDataSetType) {
        emptyDataSetType = type
    }

    /// 隐藏空数据状态
    func hideEmptyDataSet() {
        emptyDataSetType = nil
    }

    /// 根据数据数量自动设置空数据状态
    /// - Parameters:
    ///   - dataCount: 数据数量
    ///   - isLoading: 是否正在加载
    ///   - hasError: 是否有错误
    func updateEmptyDataSetForData(count dataCount: Int, isLoading: Bool = false, hasError: Bool = false) {
        if isLoading {
            emptyDataSetType = .loading
        } else if hasError {
            emptyDataSetType = .error
        } else if dataCount == 0 {
            emptyDataSetType = .noData
        } else {
            emptyDataSetType = nil
        }
    }

    // MARK: - 资源释放

    deinit {
        cancelAllRequests()
    }
}
