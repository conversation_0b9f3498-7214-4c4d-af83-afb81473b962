//
//  AssemblyService.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/6.
//

/// 服务类型枚举
enum ServiceType: Int {
    case all = 1
    case people = 2
    case photo = 3
    case rent = 4
    case misc = 5
    case car = 6
    case talk = 7
    case question = 8
}
/// 集结模块服务API
enum AssemblyService {
    case peopleList(params: RequestParametersConvertible)
    case rentList(params: RequestParametersConvertible)
    case carList(params: RequestParametersConvertible)
    case photoList(params: RequestParametersConvertible)
    case talkList(params: RequestParametersConvertible)
    case questionList(params: RequestParametersConvertible)
    case miscList(params: RequestParametersConvertible)
    case allList(params: RequestParametersConvertible) // 首页列表
    case followList(params: RequestParametersConvertible) // 关注列表
    // 详情API
    case peopleDetail(params: RequestParametersConvertible)
    case rentDetail(params: RequestParametersConvertible)
    case carDetail(params: RequestParametersConvertible)
    case photoDetail(params: RequestParametersConvertible)
    case talkDetail(params: RequestParametersConvertible)
    case questionDetail(params: RequestParametersConvertible)
    case miscDetail(params: RequestParametersConvertible)
    ///创建服务类帖子接口
    case create(params: RequestParametersConvertible)
    ///保存和更新服务类帖子草稿接口
    case saveDraft(params: RequestParametersConvertible)
    ///检查是否存在服务帖草稿接口
    case check(params: RequestParametersConvertible)
    ///更新服务类帖子接口
    case update(params: RequestParametersConvertible)
    ///添加不给谁看接口
    case visibilityBlock(params: RequestParametersConvertible)
    ///获取某帖子定向屏蔽用户列表
    case blockedUsers(params: RequestParametersConvertible)
    ///添加指定服务帖可见白名单用户接口
    case visibilityWhitelist(params: RequestParametersConvertible)
    ///获取服务帖白名单用户列表接口
    case whitelistedUsers(params: RequestParametersConvertible)
    ///删除服务帖接口（软删除）
    case delete(params: RequestParametersConvertible)
    ///获取滑雪城市与雪场列表接口
    case regionVenueList
}

extension AssemblyService: GeneralAPIService {
    var path: String {
        switch self {
        case .peopleList: return APIConstants.ServiceApi + "/people-list"
        case .rentList: return APIConstants.ServiceApi + "/rent-list"
        case .carList: return APIConstants.ServiceApi + "/car-list"
        case .photoList: return APIConstants.ServiceApi + "/photo-list"
        case .talkList: return APIConstants.ServiceApi + "/talk-list"
        case .questionList: return APIConstants.ServiceApi + "/question-list"
        case .miscList: return APIConstants.ServiceApi + "/misc-list"
        case .allList: return APIConstants.ServiceApi + "/recommend-list"
        case .followList: return APIConstants.ServiceApi + "/follow-feed"
            // 详情API路径
        case .peopleDetail: return APIConstants.ServiceDetailApi + "/people"
        case .rentDetail: return APIConstants.ServiceDetailApi + "/rent"
        case .carDetail: return APIConstants.ServiceDetailApi + "/car"
        case .photoDetail: return APIConstants.ServiceDetailApi + "/photo"
        case .talkDetail: return APIConstants.ServiceDetailApi + "/talk"
        case .questionDetail: return APIConstants.ServiceDetailApi + "/question"
        case .miscDetail: return APIConstants.ServiceDetailApi + "/misc"
            //其他
        case .create: return "/api/v1/service/create"
        case .saveDraft: return "/api/v1/service/save-draft"
        case .check: return "/api/v1/service/check"
        case .update: return APIConstants.ServiceApi + "/update"
        case .visibilityBlock: return "/api/v1/service/visibility-block"
        case .blockedUsers: return APIConstants.ServiceApi + "/blocked-users"
        case .visibilityWhitelist: return "/api/v1/service/visibility-whitelist"
        case .whitelistedUsers: return APIConstants.ServiceApi + "/whitelisted-users"
        case .delete: return APIConstants.ServiceApi + "/delete"
        case .regionVenueList: return "/api/v1/service/region-venue-list"
        }
    }
    
    var task: Task {
        switch self {
        case let .peopleList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .rentList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .carList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .photoList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .talkList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .questionList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .miscList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .allList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .followList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
            // 详情API任务
        case let .peopleDetail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .rentDetail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .carDetail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .photoDetail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .talkDetail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .questionDetail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .miscDetail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .create(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .saveDraft(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .check(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .update(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .visibilityBlock(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .blockedUsers(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .visibilityWhitelist(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .whitelistedUsers(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .delete(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case .regionVenueList:
            return .requestPlain
        }
    }
}
extension AssemblyService{
    
    /// 根据服务类型获取对应的列表API
    static func listServiceBy(type: ServiceType, params: RequestParametersConvertible) -> AssemblyService {
        switch type {
        case .people:
            return .peopleList(params: params)
        case .rent:
            return .rentList(params: params)
        case .car:
            return .carList(params: params)
        case .photo:
            return .photoList(params: params)
        case .talk:
            return .talkList(params: params)
        case .question:
            return .questionList(params: params)
        case .misc:
            return .miscList(params: params)
        case .all:
            return .allList(params: params)
        }
    }
    
    /// 根据服务类型获取对应的详情API
    static func detailServiceBy(type: ServiceType, params: RequestParametersConvertible) -> AssemblyService {
        switch type {
        case .people:
            return .peopleDetail(params: params)
        case .rent:
            return .rentDetail(params: params)
        case .car:
            return .carDetail(params: params)
        case .photo:
            return .photoDetail(params: params)
        case .talk:
            return .talkDetail(params: params)
        case .question:
            return .questionDetail(params: params)
        case .misc:
            return .miscDetail(params: params)
        case .all:
            // 全部类型默认使用peopleDetail
            return .peopleDetail(params: params)
        }
    }
}
