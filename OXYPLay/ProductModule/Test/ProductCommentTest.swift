//
//  ProductCommentTest.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

import UIKit

/// 商品评价功能测试类
class ProductCommentTest {
    
    /// 测试商品评价创建功能
    static func testProductCommentCreation() {
        print("🧪 开始测试商品评价创建功能")
        
        // 1. 测试ViewModel初始化
        let viewModel = ProductCommentViewModel(productId: 123, specValueText: "红色-L码")
        print("✅ ViewModel初始化成功")
        
        // 2. 测试评分设置
        viewModel.updateRating(4)
        assert(viewModel.rating == 4, "评分设置失败")
        print("✅ 评分设置测试通过")
        
        // 3. 测试评论内容设置
        viewModel.updateContent("这个商品质量很好，推荐购买！")
        assert(viewModel.content == "这个商品质量很好，推荐购买！", "评论内容设置失败")
        print("✅ 评论内容设置测试通过")
        
        // 4. 测试表单验证
        assert(viewModel.isFormValid() == true, "表单验证失败")
        print("✅ 表单验证测试通过")
        
        // 5. 测试评分选项获取
        let ratingOption = viewModel.getCurrentRatingOption()
        assert(ratingOption?.value == 4, "评分选项获取失败")
        assert(ratingOption?.title == "满意", "评分选项标题错误")
        print("✅ 评分选项获取测试通过")
        
        print("🎉 商品评价创建功能测试全部通过！")
    }
    
    /// 测试图片上传管理器
    static func testImageUploadManager() {
        print("🧪 开始测试图片上传管理器")
        
        // 1. 测试初始化
        let uploadManager = ImageUploadManager()
        assert(uploadManager.uploadedFileUrls.isEmpty, "初始化状态错误")
        assert(uploadManager.uploadState == .idle, "初始化状态错误")
        print("✅ 图片上传管理器初始化成功")
        
        // 2. 测试状态检查
        assert(uploadManager.isUploading() == false, "上传状态检查失败")
        print("✅ 上传状态检查测试通过")
        
        // 3. 测试重置功能
        uploadManager.resetUploadState()
        assert(uploadManager.uploadState == .idle, "重置功能失败")
        print("✅ 重置功能测试通过")
        
        print("🎉 图片上传管理器测试全部通过！")
    }
    
    /// 测试评分选项模型
    static func testRatingOptions() {
        print("🧪 开始测试评分选项模型")
        
        // 1. 测试评分选项数量
        assert(RatingOption.options.count == 5, "评分选项数量错误")
        print("✅ 评分选项数量正确")
        
        // 2. 测试各个评分选项
        let testCases = [
            (5, "非常满意", "超出期望，强烈推荐"),
            (4, "满意", "符合期望，值得推荐"),
            (3, "一般", "基本满意，有改进空间"),
            (2, "不满意", "低于期望，不太推荐"),
            (1, "非常不满意", "远低于期望，不推荐")
        ]
        
        for (rating, expectedTitle, expectedDescription) in testCases {
            let option = RatingOption.option(for: rating)
            assert(option?.value == rating, "评分值错误")
            assert(option?.title == expectedTitle, "评分标题错误")
            assert(option?.description == expectedDescription, "评分描述错误")
        }
        print("✅ 所有评分选项测试通过")
        
        // 3. 测试无效评分
        let invalidOption = RatingOption.option(for: 6)
        assert(invalidOption == nil, "无效评分处理失败")
        print("✅ 无效评分处理测试通过")
        
        print("🎉 评分选项模型测试全部通过！")
    }
    
    /// 测试请求参数构建
    static func testRequestParameters() {
        print("🧪 开始测试请求参数构建")
        
        // 1. 测试基本参数
        let request = ProductCommentCreateRequest(
            product_id: 123,
            rating: 5,
            content: "很好的商品",
            images: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
            spec_value_text: "红色-L码"
        )
        
        let params = request.asParameters()
        
        assert(params["product_id"] as? Int == 123, "商品ID参数错误")
        assert(params["rating"] as? Int == 5, "评分参数错误")
        assert(params["content"] as? String == "很好的商品", "内容参数错误")
        assert((params["images"] as? [String])?.count == 2, "图片参数错误")
        assert(params["spec_value_text"] as? String == "红色-L码", "规格参数错误")
        
        print("✅ 基本参数构建测试通过")
        
        // 2. 测试可选参数
        let minimalRequest = ProductCommentCreateRequest(
            product_id: 456,
            rating: 3,
            content: "",
            images: [],
            spec_value_text: ""
        )
        
        let minimalParams = minimalRequest.asParameters()
        assert(minimalParams["content"] == nil, "空内容参数处理错误")
        assert(minimalParams["images"] == nil, "空图片参数处理错误")
        assert(minimalParams["spec_value_text"] == nil, "空规格参数处理错误")
        
        print("✅ 可选参数处理测试通过")
        
        print("🎉 请求参数构建测试全部通过！")
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🚀 开始运行商品评价功能测试套件")
        print("=" * 50)
        
        testRatingOptions()
        print("-" * 30)
        
        testImageUploadManager()
        print("-" * 30)
        
        testProductCommentCreation()
        print("-" * 30)
        
        testRequestParameters()
        print("-" * 30)
        
        print("🎊 所有测试完成！商品评价功能实现正确。")
        print("=" * 50)
    }
}

// MARK: - String扩展（用于测试输出格式化）

extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}
