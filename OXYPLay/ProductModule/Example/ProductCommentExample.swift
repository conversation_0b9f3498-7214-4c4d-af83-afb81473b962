//
//  ProductCommentExample.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

import UIKit
import Combine

/// 商品评价功能使用示例
class ProductCommentExample {
    
    /// 示例1：在订单详情页面中使用商品评价功能
    static func exampleInOrderDetail() {
        print("📝 示例1：在订单详情页面中使用商品评价功能")
        
        // 模拟订单详情控制器
        class ExampleOrderDetailController: UIViewController {
            private var cancellables = Set<AnyCancellable>()
            
            func showCommentCreation() {
                // 创建评价控制器
                let commentController = ProductCreateCommentController()
                commentController.productId = 123
                commentController.specValueText = "红色-L码"
                
                // 监听评价完成事件
                commentController.commentCompletePublisher
                    .receive(on: DispatchQueue.main)
                    .sink { [weak self] response in
                        print("✅ 评价创建成功: \(response.id)")
                        // 这里可以刷新订单状态，更新UI等
                        self?.handleCommentCreated(response)
                    }
                    .store(in: &cancellables)
                
                // 弹出评价界面
                present(commentController, animated: true)
            }
            
            private func handleCommentCreated(_ response: ProductCommentCreateResponse) {
                // 处理评价创建成功后的逻辑
                // 例如：刷新订单详情、显示成功提示、更新按钮状态等
                print("处理评价创建成功后的逻辑")
            }
        }
        
        print("✅ 示例1完成")
    }
    
    /// 示例2：独立使用评分选择视图
    static func exampleRatingSelectionView() {
        print("📝 示例2：独立使用评分选择视图")
        
        class ExampleRatingController: UIViewController, RatingSelectionViewDelegate {
            private lazy var ratingView = RatingSelectionView().then {
                $0.delegate = self
                $0.setTitle("商品满意度")
                $0.setRequired(true)
            }
            
            override func viewDidLoad() {
                super.viewDidLoad()
                
                view.addSubview(ratingView)
                ratingView.snp.makeConstraints { make in
                    make.top.equalTo(view.safeAreaLayoutGuide).offset(20)
                    make.left.right.equalToSuperview().inset(16)
                }
                
                // 设置初始评分
                ratingView.setRating(4)
            }
            
            // MARK: - RatingSelectionViewDelegate
            
            func ratingSelectionView(_ view: RatingSelectionView, didChangeRating rating: Int) {
                print("用户选择了 \(rating) 星评分")
                
                // 根据评分显示不同的反馈
                let option = RatingOption.option(for: rating)
                print("评分描述: \(option?.description ?? "")")
            }
        }
        
        print("✅ 示例2完成")
    }
    
    /// 示例3：使用图片上传管理器
    static func exampleImageUploadManager() {
        print("📝 示例3：使用图片上传管理器")
        
        class ExampleImageUploadController: UIViewController {
            private let uploadManager = ImageUploadManager()
            private var cancellables = Set<AnyCancellable>()
            
            override func viewDidLoad() {
                super.viewDidLoad()
                setupImageUploadBindings()
            }
            
            private func setupImageUploadBindings() {
                // 监听上传状态
                uploadManager.$uploadState
                    .receive(on: DispatchQueue.main)
                    .sink { [weak self] state in
                        self?.handleUploadState(state)
                    }
                    .store(in: &cancellables)
                
                // 监听上传结果
                uploadManager.$uploadedFileUrls
                    .receive(on: DispatchQueue.main)
                    .sink { [weak self] urls in
                        self?.handleUploadedUrls(urls)
                    }
                    .store(in: &cancellables)
            }
            
            func uploadImages(_ images: [UIImage]) {
                print("开始上传 \(images.count) 张图片")
                uploadManager.uploadImages(images)
            }
            
            private func handleUploadState(_ state: RequestState) {
                switch state {
                case .idle:
                    print("上传状态：空闲")
                case .loading:
                    print("上传状态：上传中...")
                case .success:
                    print("上传状态：上传成功")
                case .failure(let message):
                    print("上传状态：上传失败 - \(message)")
                }
            }
            
            private func handleUploadedUrls(_ urls: [UploadFileResponse]) {
                print("已上传的图片URLs:")
                for (index, url) in urls.enumerated() {
                    print("  \(index + 1). \(url.url)")
                }
            }
        }
        
        print("✅ 示例3完成")
    }
    
    /// 示例4：完整的评价创建流程
    static func exampleCompleteCommentFlow() {
        print("📝 示例4：完整的评价创建流程")
        
        class ExampleCompleteFlowController: UIViewController {
            private let viewModel = ProductCommentViewModel(productId: 123, specValueText: "红色-L码")
            private var cancellables = Set<AnyCancellable>()
            
            override func viewDidLoad() {
                super.viewDidLoad()
                setupBindings()
                
                // 模拟用户操作
                simulateUserActions()
            }
            
            private func setupBindings() {
                // 监听评价创建状态
                viewModel.$createCommentState
                    .receive(on: DispatchQueue.main)
                    .sink { [weak self] state in
                        self?.handleCreateCommentState(state)
                    }
                    .store(in: &cancellables)
                
                // 监听图片上传状态
                viewModel.imageUploadManager.$uploadState
                    .receive(on: DispatchQueue.main)
                    .sink { [weak self] state in
                        self?.handleImageUploadState(state)
                    }
                    .store(in: &cancellables)
            }
            
            private func simulateUserActions() {
                print("模拟用户操作流程:")
                
                // 1. 设置评分
                print("1. 用户选择评分: 5星")
                viewModel.updateRating(5)
                
                // 2. 输入评论内容
                print("2. 用户输入评论内容")
                viewModel.updateContent("这个商品质量很好，物流也很快，非常满意！")
                
                // 3. 添加图片（模拟）
                print("3. 用户选择了图片（模拟）")
                // 在实际使用中，这里会是真实的UIImage数组
                // viewModel.addImages([image1, image2])
                
                // 4. 提交评价
                print("4. 用户点击提交评价")
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.viewModel.createComment()
                }
            }
            
            private func handleCreateCommentState(_ state: RequestState) {
                switch state {
                case .idle:
                    print("评价创建状态：空闲")
                case .loading:
                    print("评价创建状态：创建中...")
                case .success:
                    print("评价创建状态：创建成功")
                case .failure(let message):
                    print("评价创建状态：创建失败 - \(message)")
                }
            }
            
            private func handleImageUploadState(_ state: RequestState) {
                switch state {
                case .idle:
                    print("图片上传状态：空闲")
                case .loading:
                    print("图片上传状态：上传中...")
                case .success:
                    print("图片上传状态：上传成功")
                case .failure(let message):
                    print("图片上传状态：上传失败 - \(message)")
                }
            }
        }
        
        print("✅ 示例4完成")
    }
    
    /// 运行所有示例
    static func runAllExamples() {
        print("🚀 开始运行商品评价功能示例")
        print("=" * 60)
        
        exampleInOrderDetail()
        print("-" * 40)
        
        exampleRatingSelectionView()
        print("-" * 40)
        
        exampleImageUploadManager()
        print("-" * 40)
        
        exampleCompleteCommentFlow()
        print("-" * 40)
        
        print("🎉 所有示例运行完成！")
        print("=" * 60)
    }
}

// MARK: - 使用说明

/*
 使用方法：
 
 1. 在需要的地方调用示例：
    ProductCommentExample.runAllExamples()
 
 2. 在实际项目中集成：
    - 复制相关代码到你的控制器中
    - 根据实际需求调整参数和逻辑
    - 确保正确处理网络请求和错误情况
 
 3. 注意事项：
    - 确保用户已登录
    - 验证用户是否有评价权限
    - 处理网络异常情况
    - 提供良好的用户反馈
 */
