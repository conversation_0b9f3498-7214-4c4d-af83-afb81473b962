//
//  ProductOrderSellerHeaderView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/21.
//

import UIKit
import SnapKit
import Kingfisher

/// 订单列表头部视图
class ProductOrderSellerHeaderView: UITableViewHeaderFooterView {
    
    // MARK: - UI组件
  
    /// 卖家头像
    private lazy var avatarImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
        $0.layer.cornerRadius = 10
        $0.backgroundColor = color_F6F8F9
    }
    
    /// 卖家名称
    private lazy var nameLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    
    /// 容器视图
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 12
        $0.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        $0.layer.masksToBounds = true
    }
    /// 订单状态标签
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        label.textColor = UIColor(hexString: "#FF7800")
        label.text = "已完成"
        return label
    }()
    // MARK: - 初始化
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        backgroundView = UIView()
        backgroundView?.backgroundColor = color_F6F8F9
        
        contentView.addSubview(containerView)
        containerView.addSubview(statusLabel)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(nameLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.top.equalToSuperview().offset(12)
            make.bottom.equalToSuperview()
        }
        avatarImageView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalToSuperview().offset(12)
            make.width.height.equalTo(20)
            make.bottom.lessThanOrEqualToSuperview().offset(-8) 
        }
        statusLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalTo(avatarImageView)
        }
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.centerY.equalTo(avatarImageView)
            make.right.lessThanOrEqualTo(statusLabel.snp.left).offset(-8)
        }
    }

    // MARK: - 配置方法

    /// 配置头部视图数据
    func configure(with order: ProductOrderModel, orderType: OrderViewType) {
        // 设置卖家信息
        nameLabel.text = order.seller_name

        // 根据订单类型显示对应的状态文字
        if orderType == .selling {
            statusLabel.text = order.seller_status_text
        } else {
            statusLabel.text = order.buyer_status_text
        }

        // 加载卖家头像
        loadAvatar(order.seller_avatar)
    }

    // MARK: - 私有方法

    /// 加载卖家头像
    private func loadAvatar(_ urlString: String) {
        avatarImageView.setImage(url: urlString)
    }
}
/// 订单列表Footer代理
protocol ProductOrderSellerFooterViewDelegate: AnyObject {
    /// 按钮点击事件
    func footerView(_ footerView: ProductOrderSellerFooterView, didTapButton action: String, order: ProductOrderModel)
}

/// 订单列表尾部视图
class ProductOrderSellerFooterView: UITableViewHeaderFooterView {
    weak var delegate: ProductOrderSellerFooterViewDelegate?

    private var orderModel: ProductOrderModel?

    // MARK: - UI组件
    /// 容器视图
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 12
        $0.layer.maskedCorners = [.layerMaxXMaxYCorner,.layerMinXMaxYCorner]
        $0.layer.masksToBounds = true
    }
    
    /// 底部按钮StackView
    private lazy var buttonStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 12
        stackView.alignment = .center
        stackView.distribution = .fill
        return stackView
    }()
    // MARK: - 初始化
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        backgroundView = UIView()
        backgroundView?.backgroundColor = color_F6F8F9
        
        contentView.addSubview(containerView)
        containerView.addSubview(buttonStackView)
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        buttonStackView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(12)
            make.right.equalToSuperview().offset(-12)
            make.left.greaterThanOrEqualToSuperview().offset(12)
        }
    }

    // MARK: - 配置方法

    /// 配置Footer数据
    func configure(with order: ProductOrderModel) {
        self.orderModel = order
        setupButtons(with: order.action_buttons)
    }

    /// 设置按钮
    private func setupButtons(with buttons: [ActionButton]) {
        // 清空之前的按钮
        buttonStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // 如果没有按钮，隐藏StackView
        if buttons.isEmpty {
            buttonStackView.isHidden = true
            return
        }

        buttonStackView.isHidden = false

        // 添加新按钮
        for button in buttons {
            let btn = createActionButton(with: button)
            buttonStackView.addArrangedSubview(btn)
        }
    }
    
    /// 创建操作按钮
    private func createActionButton(with buttonModel: ActionButton) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(buttonModel.text, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        button.layer.cornerRadius = 14

        // 设置按钮颜色
        let titleColor = UIColor(hexString: buttonModel.title_color) ?? UIColor(hexString: "1989FA")!
        let bgColor = UIColor(hexString: buttonModel.bg_color) ?? UIColor(hexString: "E6F4FF")!

        button.setTitleColor(titleColor, for: .normal)
        button.backgroundColor = bgColor

        // 设置按钮大小
        button.snp.makeConstraints { make in
            make.height.equalTo(28)
            make.width.greaterThanOrEqualTo(60)
        }

        // 添加点击事件
        button.addTarget(self, action: #selector(actionButtonTapped(_:)), for: .touchUpInside)
        button.tag = buttonModel.action.hashValue

        return button
    }
    
    // MARK: - 事件处理

    @objc private func actionButtonTapped(_ sender: UIButton) {
        guard let order = orderModel else { return }

        // 根据tag找到对应的action
        for button in order.action_buttons {
            if button.action.hashValue == sender.tag {
                delegate?.footerView(self, didTapButton: button.action, order: order)
                break
            }
        }
    }

}
