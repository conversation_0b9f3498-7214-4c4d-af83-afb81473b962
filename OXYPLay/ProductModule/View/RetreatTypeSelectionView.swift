//
//  RetreatTypeSelectionView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

import UIKit
import Combine
import CombineCocoa

/// 退款类型枚举
enum RetreatType: Int, CaseIterable {
    case refundAndReturn = 0  // 退货退款
    case refundOnly = 1       // 仅退款
    
    var title: String {
        switch self {
        case .refundAndReturn:
            return "退货退款"
        case .refundOnly:
            return "退款"
        }
    }
}

/// 退款类型选择视图代理
protocol RetreatTypeSelectionViewDelegate: AnyObject {
    /// 退款类型切换回调
    func retreatTypeSelectionView(_ view: RetreatTypeSelectionView, didSelectType type: RetreatType)
}

/// 退款类型选择视图
class RetreatTypeSelectionView: BaseView {
    
    // MARK: - Properties
    
    weak var delegate: RetreatTypeSelectionViewDelegate?
    
    /// 当前选中的类型
    private var selectedType: RetreatType = .refundAndReturn {
        didSet {
            updateButtonStates()
        }
    }
    
    // MARK: - UI组件
    
    /// 背景容器
    lazy var containerView = UIView().then {
        $0.backgroundColor = UIColor(hexString: "788092",transparency: 0.08)
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }
    
    /// 退货退款按钮
    lazy var refundAndReturnButton = BaseButton().then {
        $0.setTitle("退货退款", for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .medium)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setTitleColor(color_blue, for: .selected)
        $0.setBackgroundColor(.white, for: .selected)
        $0.setBackgroundColor(.clear, for: .normal)
        $0.isRounded = true
        $0.isSelected = true
    }
    
    /// 仅退款按钮
    lazy var refundOnlyButton = BaseButton().then {
        $0.setTitle("退款", for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .medium)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setTitleColor(color_blue, for: .selected)
        $0.setBackgroundColor(.white, for: .selected)
        $0.setBackgroundColor(.clear, for: .normal)
        $0.isRounded = true
        $0.isSelected = false
    }
    
    // MARK: - 初始化方法
    
    override func configUI() {
        backgroundColor = .clear

        addSubview(containerView)
        containerView.addSubview(refundAndReturnButton)
        containerView.addSubview(refundOnlyButton)

        // 设置事件绑定
        setupBindings()
    }
    
    override func configLayout() {
        // 容器约束
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(32)
        }
        
        // 退货退款按钮约束
        refundAndReturnButton.snp.makeConstraints { make in
            make.left.equalTo(2)
            make.top.equalTo(2)
            make.bottom.equalTo(-2)
            make.width.equalTo(containerView).multipliedBy(0.5).offset(-2)
        }
        
        // 仅退款按钮约束
        refundOnlyButton.snp.makeConstraints { make in
            make.right.equalTo(-2)
            make.top.equalTo(2)
            make.bottom.equalTo(-2)
            make.width.equalTo(containerView).multipliedBy(0.5).offset(-2)
        }
    }
    
    override func setupBindings() {
        // 退货退款按钮点击事件
        refundAndReturnButton.tapPublisher
            .sink { [weak self] _ in
                self?.selectType(.refundAndReturn)
            }
            .store(in: &cancellables)
        
        // 仅退款按钮点击事件
        refundOnlyButton.tapPublisher
            .sink { [weak self] _ in
                self?.selectType(.refundOnly)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 选择退款类型
    /// - Parameter type: 退款类型
    func selectType(_ type: RetreatType) {
        guard selectedType != type else { return }
        
        selectedType = type
        delegate?.retreatTypeSelectionView(self, didSelectType: type)
    }
    
    /// 获取当前选中的类型
    func getSelectedType() -> RetreatType {
        return selectedType
    }
    
    /// 设置选中的类型
    /// - Parameter type: 退款类型
    func setSelectedType(_ type: RetreatType) {
        selectedType = type
    }
    
    // MARK: - 私有方法
    
    /// 更新按钮状态
    private func updateButtonStates() {
        refundAndReturnButton.isSelected = (selectedType == .refundAndReturn)
        refundOnlyButton.isSelected = (selectedType == .refundOnly)
        
        // 添加切换动画
        UIView.animate(withDuration: 0.2) {
            self.refundAndReturnButton.layoutIfNeeded()
            self.refundOnlyButton.layoutIfNeeded()
        }
    }
}
