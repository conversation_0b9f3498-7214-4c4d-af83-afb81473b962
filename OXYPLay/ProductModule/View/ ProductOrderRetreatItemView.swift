//
//   ProductOrderRetreatItemView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/1.
//

class ProductOrderRetreatItemView: BaseView {
    
    // MARK: - 属性
    
    private var item: OrderDetailItem?
    
    // MARK: - UI组件
    
    /// 基础商品信息视图
    private lazy var orderCommonView = OrderCommonView()
    /// 选择按钮
    lazy var selectButton = UIButton().then {
        $0.setImage(UIImage(named:"baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named:"baselist_single_select"), for: .selected)
    }
    
    // MARK: - UI设置
    
    override func configUI() {
        self.layer.cornerRadius = 16
        self.masksToBounds = true
        self.backgroundColor = .white
        addSubview(orderCommonView)
        addSubview(selectButton)
        setupBindings()
    }
    
    override func configLayout() {
      
        selectButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalTo(orderCommonView)
            make.width.height.equalTo(14)
        }

        orderCommonView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(selectButton.snp.right).offset(10)
            make.right.equalTo(-12)
            make.bottom.equalTo(-12)
        }
    }
    
    
    // MARK: - 配置方法
    
    /// 配置购物车商品数据
    func configure(with item: OrderDetailItem) {
        self.item = item
        // 配置基础商品信息
        orderCommonView.configModel(model: item, isProduct: 1 == item.item_type)
    }
}
