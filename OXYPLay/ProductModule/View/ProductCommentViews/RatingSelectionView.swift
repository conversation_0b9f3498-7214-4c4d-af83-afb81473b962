//
//  RatingSelectionView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

import UIKit
import Combine
import CombineCocoa

/// 评分选择视图代理协议
protocol RatingSelectionViewDelegate: AnyObject {
    /// 评分发生变化
    func ratingSelectionView(_ view: RatingSelectionView, didChangeRating rating: Int)
}

/// 评分选择视图
class RatingSelectionView: BaseView {
    
    // MARK: - Properties
    
    /// 代理对象
    weak var delegate: RatingSelectionViewDelegate?
    
    /// 当前评分
    @Published var currentRating: Int = 5
    
    /// 最大评分
    private let maxRating: Int = 5
    
    // MARK: - UI Components
    
    /// 标题标签
    private lazy var titleLabel = UILabel().then {
        $0.text = "商品评分"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
    }
    
    /// 必填标记
    private lazy var requiredLabel = UILabel().then {
        $0.text = "*"
        $0.textColor = color_red
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
    }
    
    /// 星星容器视图
    private lazy var starsContainerView = UIView()
    
    /// 星星按钮数组
    private var starButtons: [UIButton] = []
    
    /// 评分描述标签
    private lazy var ratingDescriptionLabel = UILabel().then {
        $0.textColor = color_8D9096
        $0.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        $0.textAlignment = .center
        $0.text = "超出期望，强烈推荐"
    }
    
    // MARK: - UI Configuration
    
    /// UI配置
    override func configUI() {
        backgroundColor = .white
        
        // 添加子视图
        addSubview(titleLabel)
        addSubview(requiredLabel)
        addSubview(starsContainerView)
        addSubview(ratingDescriptionLabel)
        
        // 创建星星按钮
        setupStarButtons()
        
        // 设置初始评分
        updateStarDisplay()
        updateRatingDescription()
    }
    
    /// UI布局
    override func configLayout() {
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
        }
        
        // 必填标记约束
        requiredLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel.snp.right).offset(4)
            make.centerY.equalTo(titleLabel)
        }
        
        // 星星容器约束
        starsContainerView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.height.equalTo(40)
        }
        
        // 评分描述约束
        ratingDescriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(starsContainerView.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        // 星星按钮约束
        setupStarButtonConstraints()
    }
    
    /// 设置事件绑定
    override func setupBindings() {
        // 监听评分变化
        $currentRating
            .sink { [weak self] rating in
                guard let self = self else { return }
                self.updateStarDisplay()
                self.updateRatingDescription()
                self.delegate?.ratingSelectionView(self, didChangeRating: rating)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods
    
    /// 设置星星按钮
    private func setupStarButtons() {
        starButtons.removeAll()
        
        for i in 0..<maxRating {
            let button = UIButton(type: .custom)
            // 使用系统提供的星星图标，如果没有则使用圆形作为替代
            if let emptyStarImage = UIImage(systemName: "star"),
               let filledStarImage = UIImage(systemName: "star.fill") {
                button.setImage(emptyStarImage, for: .normal)
                button.setImage(filledStarImage, for: .selected)
                button.tintColor = UIColor(hexString: "FFD700") // 金色
            } else {
                // 如果系统图标不可用，使用文字作为替代
                button.setTitle("☆", for: .normal)
                button.setTitle("★", for: .selected)
                button.setTitleColor(UIColor(hexString: "FFD700"), for: .selected)
                button.setTitleColor(color_8D9096, for: .normal)
                button.titleLabel?.font = UIFont.systemFont(ofSize: 24)
            }
            button.tag = i + 1
            
            // 使用CombineCocoa绑定点击事件
            button.tapPublisher
                .sink { [weak self] _ in
                    self?.didTapStar(rating: button.tag)
                }
                .store(in: &cancellables)
            
            starsContainerView.addSubview(button)
            starButtons.append(button)
        }
    }
    
    /// 设置星星按钮约束
    private func setupStarButtonConstraints() {
        let starSize: CGFloat = 32
        let spacing: CGFloat = 8
        let totalWidth = CGFloat(maxRating) * starSize + CGFloat(maxRating - 1) * spacing
        
        // 更新容器宽度约束
        starsContainerView.snp.makeConstraints { make in
            make.width.equalTo(totalWidth)
        }
        
        for (index, button) in starButtons.enumerated() {
            button.snp.makeConstraints { make in
                make.width.height.equalTo(starSize)
                make.centerY.equalToSuperview()
                
                if index == 0 {
                    make.left.equalToSuperview()
                } else {
                    make.left.equalTo(starButtons[index - 1].snp.right).offset(spacing)
                }
            }
        }
    }
    
    /// 星星点击事件
    /// - Parameter rating: 点击的评分
    private func didTapStar(rating: Int) {
        guard rating >= 1 && rating <= maxRating else { return }
        currentRating = rating
    }
    
    /// 更新星星显示
    private func updateStarDisplay() {
        for (index, button) in starButtons.enumerated() {
            button.isSelected = (index + 1) <= currentRating
        }
    }
    
    /// 更新评分描述
    private func updateRatingDescription() {
        if let option = RatingOption.option(for: currentRating) {
            ratingDescriptionLabel.text = option.description
        } else {
            ratingDescriptionLabel.text = ""
        }
    }
    
    // MARK: - Public Methods
    
    /// 设置评分
    /// - Parameter rating: 评分值
    func setRating(_ rating: Int) {
        guard rating >= 1 && rating <= maxRating else { return }
        currentRating = rating
    }
    
    /// 获取当前评分
    /// - Returns: 当前评分值
    func getRating() -> Int {
        return currentRating
    }
    
    /// 设置是否显示必填标记
    /// - Parameter required: 是否必填
    func setRequired(_ required: Bool) {
        requiredLabel.isHidden = !required
    }
    
    /// 设置标题
    /// - Parameter title: 标题文本
    func setTitle(_ title: String) {
        titleLabel.text = title
    }
}
