//
//  ProductOrderRetreatController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/1.
//

class ProductOrderRetreatController: BasePresentController {
    
    // MARK: - Properties
    var items:[OrderDetailItem] = [OrderDetailItem]()
    let selectCompletePublisher = PassthroughSubject<[OrderDetailItem], Never>()
    ///true为申请退货 false为创建评价
    var isRetreat = false
    override var presentationHeight: CGFloat? {
        return nil
    }
    lazy var stack = UIStackView().then{
        $0.axis = .vertical
        $0.spacing = 12
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
    }
    
    // MARK: - UI Setup
    
    override func configUI() {
        if isRetreat{
            configView(title: "选择退货商品", bottomTitle: "确认")
        }else{
            configView(title: "选择评价商品", bottomTitle: "确认")
        }
        // 添加列表视图
        contentView.addSubview(stack)
        stack.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.top.bottom.equalToSuperview()
        }
        configSubViews()
    }
    func configSubViews(){
        for (index,data) in items.enumerated(){
            let view =  ProductOrderRetreatItemView()
            view.isUserInteractionEnabled = true
            // 添加点击事件
            let tapGesture = UITapGestureRecognizer()
            tapGesture.tapPublisher
                .sink { [weak self] _ in
                    guard let self = self else { return }
                    items[index].isSelect.toggle()
                    view.selectButton.isSelected.toggle()
                }
                .store(in: &cancellables)
            
            view.addGestureRecognizer(tapGesture)
            view.configure(with: data)
            stack.addArrangedSubview(view)
        }
        
    }
    
    // MARK: - Bindings
    
    override func setupBindings() {
        super.setupBindings()
        
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self  else {
                    return
                }
                self.selectCompletePublisher.send(items)
                self.dismiss(animated: true)
                
            }
            .store(in: &cancellables)
    }
}
