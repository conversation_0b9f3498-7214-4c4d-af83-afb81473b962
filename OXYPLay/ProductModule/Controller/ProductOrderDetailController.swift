//
//  ProductOrderDetailController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/25.
//

import UIKit
import Combine
import CombineCocoa

/// 订单详情控制器
class ProductOrderDetailController: BaseViewController {
    
    // MARK: - Properties
    
    /// 订单ID
    var orderId: Int = 0

    /// ViewModel
    private var viewModel: OrderDetailViewModel!
    
    
    // MARK: - UI Components
    
    /// 主滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    /// 内容容器视图
    private lazy var contentView: UIStackView = {
        let view = UIStackView()
        view.axis = .vertical
        view.spacing = 12
        return view
    }()
    
    /// 订单状态视图
    private lazy var statusView: OrderStatusView = {
        let view = OrderStatusView()
        return view
    }()
    
    /// 收货地址视图
    private lazy var addressView: OrderAddressView = {
        let view = OrderAddressView()
        return view
    }()
  
    /// 订单信息视图
    private lazy var orderInfoView: OrderInfoView = {
        let view = OrderInfoView()
        return view
    }()
    
    /// 底部操作视图
    private lazy var bottomActionView: BaseTabToolBar = {
        let view = BaseTabToolBar()
        view.delegate = self
        return view
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // 初始化ViewModel
        viewModel = OrderDetailViewModel(orderId: orderId)

        setupUI()
        bindViewModel()
        loadData()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "订单详情"
        // 添加子视图
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addArrangedSubview(statusView)
        contentView.addArrangedSubview(addressView)
        contentView.addArrangedSubview(orderInfoView)
        
        view.addSubview(bottomActionView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomActionView.snp.top)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
        }
        
    
        bottomActionView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(80)
        }
    }
    
    private func bindViewModel() {
        // 绑定加载状态
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                if isLoading {
                    self?.showLoading()
                } else {
                    self?.hideLoading()
                }
            }
            .store(in: &cancellables)

        // 绑定请求状态
        viewModel.$requestState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                switch state {
                case .failure(let message):
                    self?.showError(message: message)
                default:
                    break
                }
            }
            .store(in: &cancellables)
        
        // 绑定订单详情数据
        viewModel.$orderDetail
            .receive(on: DispatchQueue.main)
            .sink { [weak self] orderDetail in
                if let orderDetail = orderDetail {
                    self?.updateUI(with: orderDetail)
                }
            }
            .store(in: &cancellables)
        
        // 绑定倒计时文本（已移除，现在直接在updateUI中处理）
        
      
    }
    
    private func loadData() {
        guard orderId > 0 else {
            showError(message: "订单ID无效")
            return
        }

        viewModel.loadData()
    }
    
    private func updateUI(with orderDetail: OrderDetailModel) {
        statusView.configure(orderStatus: orderDetail.status,
                           statusDetailText: orderDetail.status_detail_text,
                           statusDeadline: orderDetail.status_deadline,
                             statusMap: orderDetail.status_map)

        addressView.configure(with: orderDetail)
        orderInfoView.configure(with: orderDetail)

        // 配置底部工具栏
        configureBottomToolBar(with: orderDetail.action_buttons)
    }

    /// 配置底部工具栏
    /// - Parameter actionButtons: 操作按钮数组
    private func configureBottomToolBar(with actionButtons: [ActionButton]) {
        // 左侧固定按钮：联系卖家
        let leftItem = ToolBarButtonItem(
            normalImage: UIImage(named: "prodcut_order_chat"),
            selectedImage: UIImage(named: "prodcut_order_chat"),
            title: "联系卖家"
        )

        // 右侧按钮：根据接口数据动态设置
        let rightItems = actionButtons.enumerated().map { index, actionButton in
            ToolBarButtonItem(
                normalImage: nil,
                selectedImage: nil,
                title: actionButton.title,
                isSelected: false,
                tag: index,
                titleColor: UIColor(hexString: actionButton.title_color) ?? UIColor(hexString: "2A72FF")!,
                backgroundColor: UIColor(hexString: actionButton.bg_color) ?? UIColor.clear,
                borderColor: UIColor(hexString: actionButton.title_color) ?? UIColor(hexString: "2A72FF")!
            )
        }

        // 使用左侧一个固定按钮，右侧多个固定宽度按钮的样式
        bottomActionView.configureLeftSingleRightMultiple(leftItem: leftItem, rightItems: rightItems)
    }
    
    // MARK: - Helper Methods
    
    private func showLoading() {
        // TODO: 显示加载指示器
    }
    
    private func hideLoading() {
        // TODO: 隐藏加载指示器
    }
    
    private func showError(message: String) {
        // TODO: 显示错误提示
        print("错误: \(message)")
    }

}

// MARK: - TabToolBarDelegate

extension ProductOrderDetailController: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickLeftButtonAt index: Int, item: ToolBarButtonItem) {
        // 左侧按钮点击 - 联系卖家
        if item.title == "联系卖家" {
            print("联系卖家按钮被点击")
        }
    }

    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 右侧按钮点击 - 根据接口数据处理
        guard let orderDetail = viewModel.orderDetail,
              index < orderDetail.action_buttons.count else { return }

        let actionButton = orderDetail.action_buttons[index]
        switch actionButton.action {
        case OrderEventAction.cancel.rawValue,OrderEventAction.cancelOrder.rawValue:
            cancelOrder()
        case OrderEventAction.review.rawValue:
            createComment()
        case OrderEventAction.viewReview.rawValue:
            lookComment()
        default:
            print("")
        }
    }
    ///取消订单
    func cancelOrder(){
        viewModel.cancelOrder()
    }
    ///查看评价
    func lookComment(){
        
    }
    ///创建评价
    func createComment(){
        guard let items = viewModel.orderDetail?.items else{
            return
        }
        let vc = ProductOrderRetreatController()
        vc.items = items
        vc.isRetreat = false
        customPresent(vc, animated: true)
        present(vc, animated: true, completion: nil)
        vc.selectCompletePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] selectedItems in
                guard let self = self,
                      let selectedItemsDict = selectedItems as? [String: Any],
                      let items = selectedItemsDict["items"] as? [OrderItemModel],
                      let firstItem = items.first else { return }

                let commentVc = ProductCreateCommentController()
                commentVc.productId = Int(firstItem.product_id) ?? 0
                commentVc.specValueText = firstItem.spec_value_text

                // 监听评价完成事件
                commentVc.commentCompletePublisher
                    .receive(on: DispatchQueue.main)
                    .sink { [weak self] response in
                        print("商品评价创建成功: \(response.id)")
                        // 刷新订单详情以更新按钮状态
                        self?.viewModel.refreshOrderDetail()
                    }
                    .store(in: &self.cancellables)

                self.customPresent(commentVc, animated: true)
            }
            .store(in: &cancellables)
    }
    ///申请退货
    func retreatOrder(){
        guard let items = viewModel.orderDetail?.items else{
            return
        }
        let vc = ProductOrderRetreatController()
        vc.items = items
        customPresent(vc, animated: true)
        present(vc, animated: true, completion: nil)
        vc.selectCompletePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] items in
                let com = ProductOrderRetreatReasonController()

                
                self?.customPresent(com, animated: true)

            }
            .store(in: &cancellables)
    }
}
