//
//  CombinedPaymentController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import UIKit
import Combine

class CombinedPaymentController: BasePresentController {

    // MARK: - Properties

    /// 支付完成回调
    let paymentCompletedPublisher = PassthroughSubject<(walletAmount: Float, thirdPartyAmount: Float, paymentType: String), Never>()

    /// 订单总金额
    var totalAmount: Float = 0.0

    /// 可用余额
    var availableBalance: Float = 0.0 // 默认100元，实际应该从接口获取

    // MARK: - UI Components

    /// 支付组合视图
    private lazy var paymentView = CombinedPaymentView().then {
        $0.delegate = self
    }

    // MARK: - Lifecycle


    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        setupPaymentView()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "选择支付组合", bottomTitle: "立即支付 ¥\(String(format: "%.2f", totalAmount))")
        self.bottomButton.gradientColors = [UIColor(hexString: "FF5D48")!,UIColor(hexString: "FF3434")!]
        self.bottomButton.gradientDirection = .leftToRight

        // 添加支付视图
        contentView.addSubview(paymentView)
        paymentView.snp.makeConstraints { make in
            make.top.left.bottom.right.equalToSuperview()
        }
    }

    // MARK: - Private Methods

    /// 设置支付视图
    private func setupPaymentView() {
        paymentView.configure(
            totalAmount: totalAmount,
            availableBalance: availableBalance
        )
    }

    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        // 确认支付按钮
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.confirmPayment()
            }
            .store(in: &cancellables)
    }

    /// 确认支付
    private func confirmPayment() {
        let paymentData = paymentView.getPaymentData()

        // 验证金额
        guard paymentData.walletAmount + paymentData.thirdPartyAmount >= totalAmount - 0.01 else {
            showErrorAlert("支付金额不足")
            return
        }

        // 发送支付完成事件
        paymentCompletedPublisher.send((
            walletAmount: paymentData.walletAmount,
            thirdPartyAmount: paymentData.thirdPartyAmount,
            paymentType: paymentData.paymentType
        ))

        // 关闭弹窗
        dismiss(animated: true)
    }

    /// 显示错误提示
    override func showErrorAlert(_ message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    // MARK: - Public Methods

    /// 设置订单总金额
    func setTotalAmount(_ amount: Float) {
        totalAmount = amount
        paymentView.updateTotalAmount(amount)
        bottomButton.setTitle("立即支付 ¥\(String(format: "%.2f", amount))", for: .normal)
    }

    /// 设置可用余额
    func setAvailableBalance(_ balance: Float) {
        availableBalance = balance
        paymentView.updateAvailableBalance(balance)
    }
}

// MARK: - CombinedPaymentViewDelegate

extension CombinedPaymentController: CombinedPaymentViewDelegate {

    /// 支付金额发生变化
    func paymentView(_ view: CombinedPaymentView, didUpdatePaymentAmount amount: Float) {
        // 使用传入的实际支付金额更新按钮标题
        bottomButton.setTitle("立即支付 ¥\(String(format: "%.2f", amount))", for: .normal)
    }
}
