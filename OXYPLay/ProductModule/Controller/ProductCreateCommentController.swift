//
//  ProductCreateCommentController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

import UIKit
import Combine
import CombineCocoa

class ProductCreateCommentController: BasePresentController {

    // MARK: - Properties

    /// 商品ID
    var productId: Int = 0

    /// 款式样式文本
    var specValueText: String = ""

    /// 评价完成回调
    let commentCompletePublisher = PassthroughSubject<ProductCommentCreateResponse, Never>()

    /// ViewModel
    private var viewModel: ProductCommentViewModel!

    override var presentationHeight: CGFloat? {
        return nil
    }

    // MARK: - UI Components

    /// 评分选择视图
    private lazy var ratingSelectionView = RatingSelectionView().then {
        $0.delegate = self
    }

    /// 评论内容输入框
    private lazy var contentTextView = PlaceholderTextView().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        $0.textColor = color_2B2C2F
        $0.isScrollEnabled = false
        $0.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        $0.textContainer.lineFragmentPadding = 0
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
        $0.backgroundColor = .white
        $0.placeholder = "说说对商品的想法吧～"
    }

    /// 图片选择器视图
    private lazy var imagePickerView = ImagePickerItemView().then {
        $0.backgroundColor = .clear
        $0.addImage = UIImage(named: "prodcut_order_addimage")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // 初始化ViewModel
        viewModel = ProductCommentViewModel(productId: productId, specValueText: specValueText)

        configUI()
        setupBindings()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "编辑商品评价", bottomTitle: "发布")

        // 添加子视图
        contentView.addSubview(ratingSelectionView)
        contentView.addSubview(contentTextView)
        contentView.addSubview(imagePickerView)

        // 设置约束
        ratingSelectionView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
        }

        contentTextView.snp.makeConstraints { make in
            make.top.equalTo(ratingSelectionView.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(120)
        }

        imagePickerView.snp.makeConstraints { make in
            make.top.equalTo(contentTextView.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-12)
        }
    }

    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        // 发布按钮点击事件
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.createComment()
            }
            .store(in: &cancellables)

        // 监听评价创建状态
        viewModel.$createCommentState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                guard let self = self else { return }
                self.handleCreateCommentState(state)
            }
            .store(in: &cancellables)

        // 监听评价创建成功响应
        viewModel.$createCommentResponse
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] response in
                guard let self = self else { return }
                self.commentCompletePublisher.send(response)
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)

        // 监听内容输入变化
        contentTextView.textPublisher
            .sink { [weak self] text in
                guard let self = self else { return }
                self.viewModel.updateContent(text ?? "")
            }
            .store(in: &cancellables)

        // 监听图片选择变化 - 需要手动处理，因为ImagePickerItemView没有dataChangedPublisher
        // 这里我们通过定期检查或者其他方式来同步图片数据
    }

    // MARK: - Private Methods

    /// 创建评价
    private func createComment() {
        viewModel.createComment()
    }

    /// 处理评价创建状态
    /// - Parameter state: 创建状态
    private func handleCreateCommentState(_ state: RequestState) {
        switch state {
        case .idle:
            bottomButton.isEnabled = true
            bottomButton.setTitle("发布", for: .normal)
        case .loading:
            bottomButton.isEnabled = false
            bottomButton.setTitle("发布中...", for: .normal)
        case .success:
            bottomButton.isEnabled = true
            bottomButton.setTitle("发布", for: .normal)
        case .failure(let message):
            bottomButton.isEnabled = true
            bottomButton.setTitle("发布", for: .normal)
            showErrorAlert(message)
        }
    }

    /// 显示错误提示
    /// - Parameter message: 错误信息
    override func showErrorAlert(_ message: String) {
        // TODO: 显示错误提示弹窗
        print("评价创建失败: \(message)")
    }
}

// MARK: - RatingSelectionViewDelegate

extension ProductCreateCommentController: RatingSelectionViewDelegate {
    func ratingSelectionView(_ view: RatingSelectionView, didChangeRating rating: Int) {
        viewModel.updateRating(rating)
    }
}
