//
//  ProductCreateCommentController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

class ProductCreateCommentController: BasePresentController {
    
    // MARK: - Properties
    let selectCompletePublisher = PassthroughSubject<[String:Any], Never>()
    override var presentationHeight: CGFloat? {
        return nil
    }
   
    lazy var contentTextView = PlaceholderTextView().then {
        $0.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.isScrollEnabled = false
        $0.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        $0.textContainer.lineFragmentPadding = 0
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
        $0.backgroundColor = .white
        $0.placeholder = "说说对商品的想法吧～"
    }
    lazy var imagePikerView = ImagePickerItemView().then{
        $0.backgroundColor = .clear
        $0.addImage = UIImage(named: "prodcut_order_addimage")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
    }
    
    // MARK: - UI Setup
    
    override func configUI() {
        configView(title: "编辑商品评价", bottomTitle: "发布")

        // 添加列表视图
        contentView.addSubview(contentTextView)
        contentTextView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(280)
        }
        contentView.addSubview(imagePikerView)
        imagePikerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.top.equalTo(contentTextView.snp.bottom).offset(12)
            make.bottom.equalTo(0)
        }
    }
   
    
    // MARK: - Bindings
    
    override func setupBindings() {
        super.setupBindings()
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self  else {return}
//                self.selectCompletePublisher.send(true)
                self.dismiss(animated: true)
                
            }
            .store(in: &cancellables)
    }
}
