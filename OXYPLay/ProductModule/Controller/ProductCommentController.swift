//
//  ProductCommentController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/14.
//

import UIKit
import Combine

class ProductCommentController: BasePresentController {
    // MARK: - 属性
    
    var detailModel: ProductDetailModel?
    var viewModel: ProductViewModel?
    
    // MARK: - UI组件
    
    private lazy var tableView = UITableView().then {
        $0.backgroundColor = .clear
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self
        $0.register(ProductCommentItemTableCell.self, forCellReuseIdentifier: "ProductCommentItemTableCell")
        $0.estimatedRowHeight = 100
        $0.rowHeight = UITableView.automaticDimension
    }
    
    // MARK: - 生命周期
    
    // 实现CustomPresentable协议
    override var presentationHeight: CGFloat? {
        return kScreenHeight - 162
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        refreshData()
    }
    
    // MARK: - UI配置
    
    override func configUI() {
        configView(title: "商品评价")
        hideBottomBar()
        // 添加tableView
        contentView.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12))
        }
    }
    
    // MARK: - 数据绑定
    
   override func setupBindings() {
        guard let detailModel = detailModel else { return }
        self.viewModel = ProductViewModel(postId: detailModel.product.id , pageType: nil)
        guard let viewModel = viewModel else { return }
        // 监听评论列表变化
        viewModel.$commentList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
        
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: tableView, with: viewModel)
    }
    
    // MARK: - 数据加载
    
    private func loadMoreData() {
        viewModel?.fetchProductCommentList(refresh: false)
    }
    
    @objc private func refreshData() {
        viewModel?.fetchProductCommentList(refresh: true)
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate

extension ProductCommentController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel?.commentList.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: "ProductCommentItemTableCell", for: indexPath) as? ProductCommentItemTableCell,
              let comment = viewModel?.commentList[indexPath.row] else {
            return UITableViewCell()
        }
        
        cell.configure(with: comment)
        return cell
    }
}
