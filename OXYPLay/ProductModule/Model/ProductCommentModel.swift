//
//  ProductCommentModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

import Foundation
import SmartCodable

// MARK: - 商品评价请求模型

/// 商品评价创建请求模型
struct ProductCommentCreateRequest: SmartCodable, RequestParametersConvertible {
    /// 商品ID
    var product_id: Int = 0
    
    /// 评分（1-5）
    var rating: Int = 5
    
    /// 评论内容
    var content: String = ""
    
    /// 上传图片 URL 数组
    var images: [String] = []
    
    /// 款式样式
    var spec_value_text: String = ""
    
    func asParameters() -> [String: Any] {
        var params: [String: Any] = [
            "product_id": product_id,
            "rating": rating
        ]
        
        if !content.isEmpty {
            params["content"] = content
        }
        
        if !images.isEmpty {
            params["images"] = images
        }
        
        if !spec_value_text.isEmpty {
            params["spec_value_text"] = spec_value_text
        }
        
        return params
    }
}

// MARK: - 商品评价响应模型

/// 商品评价创建响应模型
struct ProductCommentCreateResponse: SmartCodable {
    /// 评价ID
    var id: String = ""
    
    /// 创建时间
    var created_at: String = ""
    
    /// 评价状态
    var status: String = ""
}

/// 商品评价详情模型
struct ProductCommentDetailModel: SmartCodable {
    /// 评价ID
    var id: String = ""
    
    /// 商品ID
    var product_id: String = ""
    
    /// 用户ID
    var user_id: String = ""
    
    /// 评分（1-5）
    var rating: Int = 0
    
    /// 评论内容
    var content: String = ""
    
    /// 图片URL数组
    var images: [String] = []
    
    /// 款式样式
    var spec_value_text: String = ""
    
    /// 创建时间
    var created_at: String = ""
    
    /// 更新时间
    var updated_at: String = ""
    
    /// 用户信息
    var user: CommentUserModel = CommentUserModel()
    
    /// 是否点赞
    var is_liked: Bool = false
    
    /// 点赞数量
    var like_count: Int = 0
}

/// 评价用户信息模型
struct CommentUserModel: SmartCodable {
    /// 用户ID
    var id: String = ""
    
    /// 用户昵称
    var nickname: String = ""
    
    /// 用户头像
    var avatar: String = ""
}

// MARK: - 评分选项模型

/// 评分选项模型
struct RatingOption {
    let value: Int
    let title: String
    let description: String
    
    static let options: [RatingOption] = [
        RatingOption(value: 5, title: "非常满意", description: "超出期望，强烈推荐"),
        RatingOption(value: 4, title: "满意", description: "符合期望，值得推荐"),
        RatingOption(value: 3, title: "一般", description: "基本满意，有改进空间"),
        RatingOption(value: 2, title: "不满意", description: "低于期望，不太推荐"),
        RatingOption(value: 1, title: "非常不满意", description: "远低于期望，不推荐")
    ]
    
    /// 根据评分值获取选项
    static func option(for rating: Int) -> RatingOption? {
        return options.first { $0.value == rating }
    }
}

// MARK: - 商品评价列表相关模型

/// 商品评价列表请求模型
struct ProductCommentListRequest: SmartCodable, RequestParametersConvertible {
    /// 商品ID
    var product_id: Int = 0
    
    /// 页码
    var page: Int = 1
    
    /// 每页数量
    var limit: Int = 20
    
    /// 评分筛选（可选：1-5）
    var rating: Int?
    
    /// 排序方式（可选：newest, oldest, rating_high, rating_low）
    var sort: String = "newest"
    
    func asParameters() -> [String: Any] {
        var params: [String: Any] = [
            "product_id": product_id,
            "page": page,
            "limit": limit,
            "sort": sort
        ]
        
        if let rating = rating {
            params["rating"] = rating
        }
        
        return params
    }
}

/// 商品评价列表响应模型
struct ProductCommentListResponse: SmartCodable {
    /// 评价列表
    var list: [ProductCommentDetailModel] = []
    
    /// 分页信息
    var page: Int = 1
    var size: Int = 20
    var total: Int = 0
    
    /// 评分统计
    var rating_stats: ProductRatingStats = ProductRatingStats()
}

/// 商品评分统计模型
struct ProductRatingStats: SmartCodable {
    /// 平均评分
    var average_rating: Float = 0.0
    
    /// 总评价数
    var total_count: Int = 0
    
    /// 各星级评价数量
    var rating_counts: [String: Int] = [:]
    
    /// 好评率
    var positive_rate: Float = 0.0
    
    /// 获取指定星级的评价数量
    func count(for rating: Int) -> Int {
        return rating_counts["\(rating)"] ?? 0
    }
    
    /// 获取指定星级的百分比
    func percentage(for rating: Int) -> Float {
        guard total_count > 0 else { return 0.0 }
        let count = self.count(for: rating)
        return Float(count) / Float(total_count) * 100.0
    }
}
