PODS:
  - ActiveLabel (1.1.0)
  - Alamofire (5.10.2)
  - AlipaySDK-iOS (15.8.30)
  - CombineCocoa (0.4.1)
  - DZNEmptyDataSet (1.8.1)
  - ef_swift_qrcodejs (2.3.1)
  - EFQRCode (7.0.2):
    - ef_swift_qrcodejs (~> 2.3.1)
    - SwiftDraw (~> 0.22.0)
  - <PERSON><PERSON> (2.3.0)
  - JXSegmentedView (1.4.1)
  - Kingfisher (8.3.2)
  - MBProgressHUD (1.2.0)
  - MJRefresh (3.7.9)
  - Moya/Combine (15.0.0):
    - Moya/Core
  - Moya/Core (15.0.0):
    - Alamofire (~> 5.0)
  - RongCloudIM/ChatRoom (5.22.0):
    - RongCloudIM/IMLibCore
  - RongCloudIM/CustomerService (5.22.0):
    - RongCloudIM/IMLibCore
  - RongCloudIM/Discussion (5.22.0):
    - RongCloudIM/IMLibCore
  - RongCloudIM/IMKit (5.22.0):
    - RongCloudIM/IMLib
  - RongCloudIM/IMLib (5.22.0):
    - RongCloudIM/ChatRoom
    - RongCloudIM/CustomerService
    - RongCloudIM/Discussion
    - RongCloudIM/IMLibCore
    - RongCloudIM/PublicService
  - RongCloudIM/IMLibCore (5.22.0)
  - RongCloudIM/PublicService (5.22.0):
    - RongCloudIM/IMLibCore
  - SmartCodable (5.0.14):
    - SmartCodable/Core (= 5.0.14)
  - SmartCodable/Core (5.0.14)
  - SnapKit (5.7.1)
  - SwiftDate (7.0.0)
  - SwiftDraw (0.22.0):
    - SwiftDrawDOM (~> 0.22.0)
  - SwiftDrawDOM (0.22.0)
  - SwifterSwift (7.0.0):
    - SwifterSwift/AppKit (= 7.0.0)
    - SwifterSwift/CoreAnimation (= 7.0.0)
    - SwifterSwift/CoreGraphics (= 7.0.0)
    - SwifterSwift/CoreLocation (= 7.0.0)
    - SwifterSwift/CryptoKit (= 7.0.0)
    - SwifterSwift/Dispatch (= 7.0.0)
    - SwifterSwift/Foundation (= 7.0.0)
    - SwifterSwift/HealthKit (= 7.0.0)
    - SwifterSwift/MapKit (= 7.0.0)
    - SwifterSwift/SceneKit (= 7.0.0)
    - SwifterSwift/SpriteKit (= 7.0.0)
    - SwifterSwift/StoreKit (= 7.0.0)
    - SwifterSwift/SwiftStdlib (= 7.0.0)
    - SwifterSwift/UIKit (= 7.0.0)
    - SwifterSwift/WebKit (= 7.0.0)
  - SwifterSwift/AppKit (7.0.0)
  - SwifterSwift/CoreAnimation (7.0.0)
  - SwifterSwift/CoreGraphics (7.0.0)
  - SwifterSwift/CoreLocation (7.0.0)
  - SwifterSwift/CryptoKit (7.0.0)
  - SwifterSwift/Dispatch (7.0.0)
  - SwifterSwift/Foundation (7.0.0)
  - SwifterSwift/HealthKit (7.0.0)
  - SwifterSwift/MapKit (7.0.0)
  - SwifterSwift/SceneKit (7.0.0)
  - SwifterSwift/SpriteKit (7.0.0)
  - SwifterSwift/StoreKit (7.0.0)
  - SwifterSwift/SwiftStdlib (7.0.0)
  - SwifterSwift/UIKit (7.0.0)
  - SwifterSwift/WebKit (7.0.0)
  - SwiftyJSON (5.0.2)
  - Then (3.0.0)
  - WechatOpenSDK (2.0.4)
  - ZLPhotoBrowser (*******):
    - ZLPhotoBrowser/Core (= *******)
  - ZLPhotoBrowser/Core (*******)

DEPENDENCIES:
  - ActiveLabel
  - AlipaySDK-iOS
  - CombineCocoa
  - DZNEmptyDataSet
  - EFQRCode
  - Jelly
  - JXSegmentedView
  - Kingfisher
  - MBProgressHUD
  - MJRefresh
  - Moya/Combine (~> 15.0)
  - RongCloudIM/IMKit
  - SmartCodable
  - SnapKit
  - SwiftDate
  - SwifterSwift
  - SwiftyJSON
  - Then
  - WechatOpenSDK
  - ZLPhotoBrowser

SPEC REPOS:
  https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git:
    - AlipaySDK-iOS
    - DZNEmptyDataSet
    - ef_swift_qrcodejs
    - EFQRCode
    - Jelly
    - RongCloudIM
    - SwiftDraw
    - SwiftDrawDOM
    - WechatOpenSDK
    - ZLPhotoBrowser
  trunk:
    - ActiveLabel
    - Alamofire
    - CombineCocoa
    - JXSegmentedView
    - Kingfisher
    - MBProgressHUD
    - MJRefresh
    - Moya
    - SmartCodable
    - SnapKit
    - SwiftDate
    - SwifterSwift
    - SwiftyJSON
    - Then

SPEC CHECKSUMS:
  ActiveLabel: 5e3f4de79a1952d4604b845a0610d4776e4b82b3
  Alamofire: 7193b3b92c74a07f85569e1a6c4f4237291e7496
  AlipaySDK-iOS: 913a1df564c64798903e71cf3d63141d9e1904d2
  CombineCocoa: e5210dbfb480ff251078929459ac17e750e7af1d
  DZNEmptyDataSet: 9525833b9e68ac21c30253e1d3d7076cc828eaa7
  ef_swift_qrcodejs: bcc43c1bfc5e29f20ea4bb8239f55c95421b3ec3
  EFQRCode: b20458e7c4227ddbd6848656a7730aa47cec818a
  Jelly: 5c6315db07d392b178ebf9edf27072a294d4f434
  JXSegmentedView: cd73555ce2134d1656db2cb383ba9c2f36fb5078
  Kingfisher: 0621d0ac0c78fecb19f6dc5303bde2b52abaf2f5
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  MJRefresh: ff9e531227924c84ce459338414550a05d2aea78
  Moya: 138f0573e53411fb3dc17016add0b748dfbd78ee
  RongCloudIM: 6293b922504fbf4716545cce9237b4083165d034
  SmartCodable: 384edba3cf436a430b35049962685b08ad3ed5cc
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  SwiftDate: bbc26e26fc8c0c33fbee8c140c5e8a68293a148a
  SwiftDraw: cae7cb61b4afd5fa7b5ea1582041dd1c9c2d5823
  SwiftDrawDOM: e73531bc6b02a3a6a3169ae1a12487e65a0c1261
  SwifterSwift: e9caf990fc72e835432280755d1f4c43f2a483d5
  SwiftyJSON: f5b1bf1cd8dd53cd25887ac0eabcfd92301c6a5a
  Then: 844265ae87834bbe1147d91d5d41a404da2ec27d
  WechatOpenSDK: 290989072e87b79d52225a01bb4e5e58f6f88593
  ZLPhotoBrowser: 20f32e6429448cc1c008795a1b55472d5772939c

PODFILE CHECKSUM: 14c476ce1b038a89e9a871af0f178f0fc6033f8e

COCOAPODS: 1.16.2
